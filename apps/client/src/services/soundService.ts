class SoundService {
  private audioContext: AudioContext | null = null;
  private enabled = true;
  private volume = 0.3;

  constructor() {
    // Initialize audio context on first user interaction
    this.initializeAudioContext();
  }

  private initializeAudioContext() {
    const initAudio = () => {
      if (!this.audioContext) {
        this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      }
      document.removeEventListener('click', initAudio);
      document.removeEventListener('keydown', initAudio);
    };

    document.addEventListener('click', initAudio);
    document.addEventListener('keydown', initAudio);
  }

  setEnabled(enabled: boolean) {
    this.enabled = enabled;
  }

  setVolume(volume: number) {
    this.volume = Math.max(0, Math.min(1, volume));
  }

  private createTone(frequency: number, duration: number, type: OscillatorType = 'sine') {
    if (!this.enabled || !this.audioContext) return;

    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
    oscillator.type = type;

    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(this.volume, this.audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + duration);
  }

  private createChord(frequencies: number[], duration: number, type: OscillatorType = 'sine') {
    frequencies.forEach(freq => this.createTone(freq, duration, type));
  }

  // Card selection sound
  cardSelect() {
    this.createTone(800, 0.1, 'sine');
  }

  // Card deselection sound
  cardDeselect() {
    this.createTone(600, 0.1, 'sine');
  }

  // Card flip sound
  cardFlip() {
    this.createTone(400, 0.15, 'triangle');
    setTimeout(() => this.createTone(500, 0.1, 'triangle'), 50);
  }

  // Show cards sound (success)
  showCards() {
    this.createChord([523, 659, 784], 0.3, 'sine'); // C major chord
  }

  // Scout sound
  scout() {
    this.createTone(880, 0.2, 'sawtooth');
    setTimeout(() => this.createTone(1100, 0.15, 'sawtooth'), 100);
  }

  // Pass sound
  pass() {
    this.createTone(300, 0.2, 'triangle');
  }

  // Round end sound
  roundEnd() {
    const notes = [523, 587, 659, 698, 784]; // C D E F G
    notes.forEach((note, i) => {
      setTimeout(() => this.createTone(note, 0.2, 'sine'), i * 100);
    });
  }

  // Game win sound with confetti trigger
  gameWin() {
    // Victory fanfare
    const melody = [
      { freq: 523, time: 0 },    // C
      { freq: 659, time: 200 },  // E
      { freq: 784, time: 400 },  // G
      { freq: 1047, time: 600 }, // C (octave)
      { freq: 784, time: 800 },  // G
      { freq: 1047, time: 1000 } // C (octave)
    ];

    melody.forEach(({ freq, time }) => {
      setTimeout(() => this.createTone(freq, 0.3, 'sine'), time);
    });

    // Trigger confetti
    this.triggerConfetti();
  }

  // Error sound
  error() {
    this.createTone(200, 0.3, 'sawtooth');
  }

  // Button hover sound
  buttonHover() {
    this.createTone(1200, 0.05, 'sine');
  }

  // Notification sound
  notification() {
    this.createTone(800, 0.1, 'sine');
    setTimeout(() => this.createTone(1000, 0.1, 'sine'), 100);
  }

  private triggerConfetti() {
    // Create confetti effect
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];
    const confettiCount = 50;

    for (let i = 0; i < confettiCount; i++) {
      setTimeout(() => {
        this.createConfettiPiece(colors[Math.floor(Math.random() * colors.length)]);
      }, Math.random() * 1000);
    }
  }

  private createConfettiPiece(color: string) {
    const confetti = document.createElement('div');
    confetti.style.position = 'fixed';
    confetti.style.left = Math.random() * 100 + 'vw';
    confetti.style.top = '-10px';
    confetti.style.width = '10px';
    confetti.style.height = '10px';
    confetti.style.backgroundColor = color;
    confetti.style.pointerEvents = 'none';
    confetti.style.zIndex = '9999';
    confetti.style.borderRadius = '50%';
    confetti.style.transform = `rotate(${Math.random() * 360}deg)`;

    document.body.appendChild(confetti);

    // Animate the confetti falling
    const animation = confetti.animate([
      {
        transform: `translateY(0px) rotate(0deg)`,
        opacity: 1
      },
      {
        transform: `translateY(${window.innerHeight + 100}px) rotate(${360 + Math.random() * 360}deg)`,
        opacity: 0
      }
    ], {
      duration: 2000 + Math.random() * 1000,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    });

    animation.onfinish = () => {
      confetti.remove();
    };
  }

  // Play a sequence of sounds for dramatic effect
  playSequence(sounds: Array<{ sound: keyof SoundService; delay: number }>) {
    sounds.forEach(({ sound, delay }) => {
      setTimeout(() => {
        if (typeof this[sound] === 'function') {
          (this[sound] as Function)();
        }
      }, delay);
    });
  }
}

export const soundService = new SoundService();
