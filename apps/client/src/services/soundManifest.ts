// Sound manifest system for production-grade SFX
export interface SoundAsset {
  id: string;
  title: string;
  path: string;
  format: 'wav' | 'ogg' | 'mp3';
  license: 'CC0' | 'CC-BY' | 'ROYALTY_FREE';
  source: 'kenney' | 'freesound' | 'sonniss' | 'opengameart' | 'custom';
  tags: string[];
  duration?: number;
  volume?: number; // Default volume multiplier (0.0 - 1.0)
  category: 'ui' | 'card' | 'token' | 'game' | 'ambient';
}

export interface SoundManifest {
  version: string;
  assets: { [id: string]: SoundAsset };
  events: { [eventName: string]: string }; // Maps game events to asset IDs
}

// Production sound manifest - will be populated with actual SFX files
export const soundManifest: SoundManifest = {
  version: "1.0.0",
  assets: {
    // UI Sounds
    "ui_click_soft": {
      id: "ui_click_soft",
      title: "UI Click Soft",
      path: "/assets/sfx/ui_click_soft.ogg",
      format: "ogg",
      license: "CC0",
      source: "kenney",
      tags: ["ui", "click", "button", "soft"],
      volume: 0.6,
      category: "ui"
    },
    "ui_click_sharp": {
      id: "ui_click_sharp",
      title: "UI Click Sharp",
      path: "/assets/sfx/ui_click_sharp.ogg",
      format: "ogg",
      license: "CC0",
      source: "kenney",
      tags: ["ui", "click", "button", "sharp"],
      volume: 0.5,
      category: "ui"
    },
    "ui_confirmation": {
      id: "ui_confirmation",
      title: "UI Confirmation",
      path: "/assets/sfx/ui_confirmation.ogg",
      format: "ogg",
      license: "CC0",
      source: "kenney",
      tags: ["ui", "confirmation", "success", "positive"],
      volume: 0.7,
      category: "ui"
    },
    "ui_error": {
      id: "ui_error",
      title: "UI Error",
      path: "/assets/sfx/ui_error.ogg",
      format: "ogg",
      license: "CC0",
      source: "kenney",
      tags: ["ui", "error", "negative", "fail"],
      volume: 0.6,
      category: "ui"
    },
    "ui_whoosh": {
      id: "ui_whoosh",
      title: "UI Whoosh",
      path: "/assets/sfx/ui_whoosh.ogg",
      format: "ogg",
      license: "CC0",
      source: "kenney",
      tags: ["ui", "whoosh", "transition", "swipe"],
      volume: 0.5,
      category: "ui"
    },

    // Card Sounds
    "card_flip": {
      id: "card_flip",
      title: "Card Flip",
      path: "/assets/sfx/card_flip.ogg",
      format: "ogg",
      license: "CC0",
      source: "opengameart",
      tags: ["card", "flip", "paper", "turn"],
      volume: 0.8,
      category: "card"
    },
    "card_slide": {
      id: "card_slide",
      title: "Card Slide",
      path: "/assets/sfx/card_slide.ogg",
      format: "ogg",
      license: "ROYALTY_FREE",
      source: "sonniss",
      tags: ["card", "slide", "smooth", "paper"],
      volume: 0.6,
      category: "card"
    },
    "card_shuffle": {
      id: "card_shuffle",
      title: "Card Shuffle",
      path: "/assets/sfx/card_shuffle.ogg",
      format: "ogg",
      license: "CC0",
      source: "opengameart",
      tags: ["card", "shuffle", "paper", "rustle"],
      volume: 0.7,
      category: "card"
    },

    // Token/Piece Sounds
    "wood_tap_light": {
      id: "wood_tap_light",
      title: "Wood Tap Light",
      path: "/assets/sfx/wood_tap_light.ogg",
      format: "ogg",
      license: "ROYALTY_FREE",
      source: "sonniss",
      tags: ["wood", "tap", "light", "impact", "token"],
      volume: 0.8,
      category: "token"
    },

    // Game State Sounds
    "success_chime": {
      id: "success_chime",
      title: "Success Chime",
      path: "/assets/sfx/success_chime.ogg",
      format: "ogg",
      license: "CC0",
      source: "opengameart",
      tags: ["success", "chime", "positive", "achievement"],
      volume: 0.8,
      category: "game"
    },
    "victory_fanfare": {
      id: "victory_fanfare",
      title: "Victory Fanfare",
      path: "/assets/sfx/victory_fanfare.ogg",
      format: "ogg",
      license: "ROYALTY_FREE",
      source: "sonniss",
      tags: ["victory", "fanfare", "success", "celebration"],
      volume: 0.9,
      category: "game"
    },
    "round_transition": {
      id: "round_transition",
      title: "Round Transition",
      path: "/assets/sfx/round_transition.ogg",
      format: "ogg",
      license: "CC0",
      source: "kenney",
      tags: ["transition", "whoosh", "round", "game"],
      volume: 0.6,
      category: "game"
    }
  },
  events: {
    // Card events
    "card.select": "ui_click_soft",
    "card.deselect": "ui_click_soft",
    "card.flip": "card_flip",
    "card.slide": "card_slide",
    
    // Game actions
    "action.show": "ui_confirmation",
    "action.scout": "ui_whoosh",
    "action.pass": "ui_click_sharp",
    
    // Game states
    "game.error": "ui_error",
    "game.success": "success_chime",
    "game.victory": "victory_fanfare",
    "round.end": "round_transition",
    "round.start": "round_transition",
    
    // UI interactions
    "ui.hover": "ui_click_soft",
    "ui.notification": "ui_confirmation",
    
    // Token/piece interactions
    "token.place": "wood_tap_light",
    "token.remove": "wood_tap_light",
    
    // Card management
    "cards.shuffle": "card_shuffle"
  }
};

// Helper functions for sound management
export function getSoundAsset(eventName: string): SoundAsset | null {
  const assetId = soundManifest.events[eventName];
  if (!assetId) return null;
  
  return soundManifest.assets[assetId] || null;
}

export function getAllSoundAssets(): SoundAsset[] {
  return Object.values(soundManifest.assets);
}

export function getSoundAssetsByCategory(category: SoundAsset['category']): SoundAsset[] {
  return Object.values(soundManifest.assets).filter(asset => asset.category === category);
}

export function getSoundAssetsBySource(source: SoundAsset['source']): SoundAsset[] {
  return Object.values(soundManifest.assets).filter(asset => asset.source === source);
}

export function getSoundAssetsByLicense(license: SoundAsset['license']): SoundAsset[] {
  return Object.values(soundManifest.assets).filter(asset => asset.license === license);
}
