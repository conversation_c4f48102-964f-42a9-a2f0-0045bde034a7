// mcp-sfx-server/index.ts
import { createServer } from "mcp-server";
import { searchFreesound, dlFreesound } from "./sources/freesound";
import { listKenney, getKenney } from "./sources/kenney";
import { listSonniss, getSonniss } from "./sources/sonniss";
import { normalizeAudio } from "./util/ffmpeg";

type Query = { q: string; license?: ("CC0"|"CC-BY"|"ROYALTY_FREE")[]; limit?: number };
type DLReq = { id: string; source: "freesound"|"kenney"|"sonniss"; format?: "wav"|"ogg"; mono?: boolean };

const server = createServer({
  tools: {
    search_sfx: {
      description: "Search SFX across providers",
      inputSchema: { type:"object", properties:{ q:{type:"string"}, license:{type:"array"}, limit:{type:"number"} }, required:["q"] },
      handler: async ({ q, license = ["CC0","ROYALTY_FREE","CC-BY"], limit = 20 } : Query) => {
        const [k, s, f] = await Promise.all([
          listKenney(q), listSonniss(q), searchFreesound(q, license, limit)
        ]);
        return [...k, ...s, ...f].slice(0, limit);
      }
    },
    download_sfx: {
      description: "Download a specific SFX and return a local path",
      inputSchema: { type:"object", properties:{ id:{type:"string"}, source:{type:"string"}, format:{type:"string"}, mono:{type:"boolean"}}, required:["id","source"] },
      handler: async (req: DLReq) => {
        const rawPath = req.source === "kenney" ? await getKenney(req.id)
                       : req.source === "sonniss" ? await getSonniss(req.id)
                       : await dlFreesound(req.id);
        const out = await normalizeAudio(rawPath, { rate: 48000, peakDb: -3, mono: !!req.mono, format: req.format ?? "wav" });
        return { path: out };
      }
    },
    catalog_sfx: {
      description: "List recommended SFX for common board-game events",
      inputSchema: { type:"object", properties:{} },
      handler: async () => ({
        // Categories the agent can request directly
        events: {
          "card/slide": ["ui/swish/soft","paper/slide"],
          "card/flip": ["ui/flip/cardboard"],
          "set/score": ["ui/confirm/soft","bell/dry"],
          "illegal/move": ["ui/error/click","buzzer/soft"],
          "round/start": ["ui/whoosh/medium"],
          "round/end": ["stinger/short/neutral"],
          "token/place": ["wood/tap/light","chip/stack"],
          "token/remove": ["wood/lift","chip/pickup"],
        }
      })
    }
  }
});

server.listen();
