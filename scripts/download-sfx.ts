#!/usr/bin/env tsx
// Script to download production SFX files using the MCP SFX server
import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { soundManifest } from '../apps/client/src/services/soundManifest.js';

interface MCPRequest {
  jsonrpc: string;
  id: number;
  method: string;
  params?: any;
}

interface MCPResponse {
  jsonrpc: string;
  id: number;
  result?: any;
  error?: any;
}

class MCPClient {
  private process: any;
  private requestId = 1;
  private pendingRequests = new Map<number, { resolve: Function; reject: Function }>();

  constructor(private serverPath: string) {}

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.process = spawn('tsx', [this.serverPath], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.process.stderr.on('data', (data: Buffer) => {
        console.error('Server stderr:', data.toString());
      });

      this.process.stdout.on('data', (data: Buffer) => {
        const lines = data.toString().split('\n').filter(line => line.trim());
        for (const line of lines) {
          try {
            const response: MCPResponse = JSON.parse(line);
            const pending = this.pendingRequests.get(response.id);
            if (pending) {
              this.pendingRequests.delete(response.id);
              if (response.error) {
                pending.reject(new Error(response.error.message || 'MCP Error'));
              } else {
                pending.resolve(response.result);
              }
            }
          } catch (error) {
            console.warn('Failed to parse MCP response:', line);
          }
        }
      });

      this.process.on('error', reject);
      this.process.on('spawn', () => {
        // Initialize the MCP connection
        this.initialize().then(resolve).catch(reject);
      });
    });
  }

  private async initialize(): Promise<void> {
    await this.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: {
        name: 'sfx-downloader',
        version: '1.0.0'
      }
    });
  }

  private async sendRequest(method: string, params?: any): Promise<any> {
    const id = this.requestId++;
    const request: MCPRequest = {
      jsonrpc: '2.0',
      id,
      method,
      params
    };

    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject });
      this.process.stdin.write(JSON.stringify(request) + '\n');
      
      // Timeout after 30 seconds
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request timeout for ${method}`));
        }
      }, 30000);
    });
  }

  async searchSfx(query: string, license?: string[], limit?: number): Promise<any> {
    return this.sendRequest('tools/call', {
      name: 'search_sfx',
      arguments: { q: query, license, limit }
    });
  }

  async downloadSfx(id: string, source: string, format?: string, mono?: boolean): Promise<any> {
    return this.sendRequest('tools/call', {
      name: 'download_sfx',
      arguments: { id, source, format, mono }
    });
  }

  async catalogSfx(): Promise<any> {
    return this.sendRequest('tools/call', {
      name: 'catalog_sfx',
      arguments: {}
    });
  }

  stop(): void {
    if (this.process) {
      this.process.kill();
    }
  }
}

async function downloadSfxFiles(): Promise<void> {
  const serverPath = path.join(__dirname, '../exp/mcp-sfx-server/index.ts');
  const outputDir = path.join(__dirname, '../apps/client/public/assets/sfx');
  
  // Ensure output directory exists
  await fs.mkdir(outputDir, { recursive: true });
  
  console.log('Starting MCP SFX Server...');
  const client = new MCPClient(serverPath);
  
  try {
    await client.start();
    console.log('MCP SFX Server started successfully');
    
    // Get catalog to see available sounds
    console.log('\nGetting SFX catalog...');
    const catalog = await client.catalogSfx();
    console.log('Catalog:', JSON.stringify(catalog, null, 2));
    
    // Download sounds for each asset in our manifest
    const assets = Object.values(soundManifest.assets);
    console.log(`\nDownloading ${assets.length} sound assets...`);
    
    for (const asset of assets) {
      console.log(`\nProcessing ${asset.id} (${asset.title})...`);
      
      try {
        // Search for the sound based on tags
        const searchQuery = asset.tags.slice(0, 3).join(' '); // Use first 3 tags
        console.log(`  Searching for: "${searchQuery}"`);
        
        const searchResult = await client.searchSfx(searchQuery, [asset.license], 5);
        const searchData = JSON.parse(searchResult.content[0].text);
        
        if (searchData.results && searchData.results.length > 0) {
          const bestMatch = searchData.results[0];
          console.log(`  Found: ${bestMatch.title} from ${bestMatch.source}`);
          
          // Download the sound
          const downloadResult = await client.downloadSfx(
            bestMatch.id,
            bestMatch.source,
            asset.format,
            asset.category === 'ui' // Make UI sounds mono
          );
          
          const downloadData = JSON.parse(downloadResult.content[0].text);
          const sourcePath = downloadData.path;
          const targetPath = path.join(outputDir, path.basename(asset.path));
          
          // Copy the file to our assets directory
          await fs.copyFile(sourcePath, targetPath);
          console.log(`  ✓ Downloaded to: ${targetPath}`);
          
        } else {
          console.log(`  ⚠ No results found for "${searchQuery}"`);
        }
        
      } catch (error) {
        console.error(`  ✗ Failed to download ${asset.id}:`, error);
      }
      
      // Small delay to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('\n✓ SFX download process completed!');
    
  } catch (error) {
    console.error('Failed to download SFX files:', error);
    process.exit(1);
  } finally {
    client.stop();
  }
}

// Run the script
if (require.main === module) {
  downloadSfxFiles().catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}
