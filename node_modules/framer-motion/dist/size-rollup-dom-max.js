import{q as t,t as e,u as i,v as n,w as s,x as o,y as r,z as a,A as l,B as h,C as u,D as c,b as d,m as p,o as m,r as f,E as y,n as g,i as v,h as x,p as T,k as P,F as w,d as S,s as A,P as b,L as E,S as V,G as M,a as D,H as C,j as k,I as R,J as L,l as j,e as B,f as F,g as I}from"./size-rollup-dom-max-assets.js";import{jsx as O}from"react/jsx-runtime";import{useContext as U,useId as N,useEffect as K,useCallback as W,Component as $,Fragment as z}from"react";function Y(t,e){-1===t.indexOf(e)&&t.push(e)}function X(t,e){const i=t.indexOf(e);i>-1&&t.splice(i,1)}const H={},G=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function q(t){return"object"==typeof t&&null!==t}const _=t=>/^0[^.\s]+$/u.test(t);function Z(t){let e;return()=>(void 0===e&&(e=t()),e)}const J=t=>t,Q=(t,e)=>i=>e(t(i)),tt=(...t)=>t.reduce(Q),et=(t,e,i)=>{const n=e-t;return 0===n?1:(i-t)/n};class it{constructor(){this.subscriptions=[]}add(t){return Y(this.subscriptions,t),()=>X(this.subscriptions,t)}notify(t,e,i){const n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let s=0;s<n;s++){const n=this.subscriptions[s];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const nt=t=>1e3*t,st=t=>t/1e3;function ot(t,e){return e?t*(1e3/e):0}const rt=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function at(t,e,i,n){if(t===e&&i===n)return J;const s=e=>function(t,e,i,n,s){let o,r,a=0;do{r=e+(i-e)/2,o=rt(r,n,s)-t,o>0?i=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,i);return t=>0===t||1===t?t:rt(s(t),e,n)}const lt=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,ht=t=>e=>1-t(1-e),ut=at(.33,1.53,.69,.99),ct=ht(ut),dt=lt(ct),pt=t=>(t*=2)<1?.5*ct(t):.5*(2-Math.pow(2,-10*(t-1))),mt=t=>1-Math.sin(Math.acos(t)),ft=ht(mt),yt=lt(mt),gt=at(.42,0,1,1),vt=at(0,0,.58,1),xt=at(.42,0,.58,1),Tt=t=>Array.isArray(t)&&"number"==typeof t[0],Pt={linear:J,easeIn:gt,easeInOut:xt,easeOut:vt,circIn:mt,circInOut:yt,circOut:ft,backIn:ct,backInOut:dt,backOut:ut,anticipate:pt},wt=t=>{if(Tt(t)){t.length;const[e,i,n,s]=t;return at(e,i,n,s)}return"string"==typeof t?Pt[t]:t},St=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],At={value:null,addProjectionMetrics:null};function bt(t,e){let i=!1,n=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>i=!0,r=St.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){r.has(e)&&(u.schedule(e),t()),l++,e(a)}const u={schedule:(t,e=!1,o=!1)=>{const a=o&&s?i:n;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[i,n]=[n,i],i.forEach(h),e&&At.value&&At.value.frameloop[e].push(l),l=0,i.clear(),s=!1,o&&(o=!1,u.process(t)))}};return u}(o,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:h,preUpdate:u,update:c,preRender:d,render:p,postRender:m}=r,f=()=>{const o=H.useManualTiming?s.timestamp:performance.now();i=!1,H.useManualTiming||(s.delta=n?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),h.process(s),u.process(s),c.process(s),d.process(s),p.process(s),m.process(s),s.isProcessing=!1,i&&e&&(n=!1,t(f))};return{schedule:St.reduce((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(i||(i=!0,n=!0,s.isProcessing||t(f)),a.schedule(e,o,r)),e},{}),cancel:t=>{for(let e=0;e<St.length;e++)r[St[e]].cancel(t)},state:s,steps:r}}const{schedule:Et,cancel:Vt,state:Mt,steps:Dt}=bt("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:J,!0);let Ct;function kt(){Ct=void 0}const Rt={now:()=>(void 0===Ct&&Rt.set(Mt.isProcessing||H.useManualTiming?Mt.timestamp:performance.now()),Ct),set:t=>{Ct=t,queueMicrotask(kt)}},Lt=t=>Math.round(1e5*t)/1e5,jt=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const Bt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ft=(t,e)=>i=>Boolean("string"==typeof i&&Bt.test(i)&&i.startsWith(t)||e&&!function(t){return null==t}(i)&&Object.prototype.hasOwnProperty.call(i,e)),It=(t,e,i)=>n=>{if("string"!=typeof n)return n;const[s,o,r,a]=n.match(jt);return{[t]:parseFloat(s),[e]:parseFloat(o),[i]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},Ot={...t,transform:t=>Math.round((t=>i(0,255,t))(t))},Ut={test:Ft("rgb","red"),parse:It("red","green","blue"),transform:({red:t,green:i,blue:n,alpha:s=1})=>"rgba("+Ot.transform(t)+", "+Ot.transform(i)+", "+Ot.transform(n)+", "+Lt(e.transform(s))+")"};const Nt={test:Ft("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:Ut.transform},Kt={test:Ft("hsl","hue"),parse:It("hue","saturation","lightness"),transform:({hue:t,saturation:i,lightness:s,alpha:o=1})=>"hsla("+Math.round(t)+", "+n.transform(Lt(i))+", "+n.transform(Lt(s))+", "+Lt(e.transform(o))+")"},Wt={test:t=>Ut.test(t)||Nt.test(t)||Kt.test(t),parse:t=>Ut.test(t)?Ut.parse(t):Kt.test(t)?Kt.parse(t):Nt.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?Ut.transform(t):Kt.transform(t),getAnimatableNone:t=>{const e=Wt.parse(t);return e.alpha=0,Wt.transform(e)}},$t=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const zt="number",Yt="color",Xt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ht(t){const e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(Xt,t=>(Wt.test(t)?(n.color.push(o),s.push(Yt),i.push(Wt.parse(t))):t.startsWith("var(")?(n.var.push(o),s.push("var"),i.push(t)):(n.number.push(o),s.push(zt),i.push(parseFloat(t))),++o,"${}")).split("${}");return{values:i,split:r,indexes:n,types:s}}function Gt(t){return Ht(t).values}function qt(t){const{split:e,types:i}=Ht(t),n=e.length;return t=>{let s="";for(let o=0;o<n;o++)if(s+=e[o],void 0!==t[o]){const e=i[o];s+=e===zt?Lt(t[o]):e===Yt?Wt.transform(t[o]):t[o]}return s}}const _t=t=>"number"==typeof t?0:Wt.test(t)?Wt.getAnimatableNone(t):t;const Zt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(jt)?.length||0)+(t.match($t)?.length||0)>0},parse:Gt,createTransformer:qt,getAnimatableNone:function(t){const e=Gt(t);return qt(t)(e.map(_t))}};function Jt(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function Qt(t,e){return i=>i>0?e:t}const te=(t,e,i)=>t+(e-t)*i,ee=(t,e,i)=>{const n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},ie=[Nt,Ut,Kt];function ne(t){const e=(i=t,ie.find(t=>t.test(i)));var i;if(!Boolean(e))return!1;let n=e.parse(t);return e===Kt&&(n=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,o=0,r=0;if(e/=100){const n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;s=Jt(a,n,t+1/3),o=Jt(a,n,t),r=Jt(a,n,t-1/3)}else s=o=r=i;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:n}}(n)),n}const se=(t,e)=>{const i=ne(t),n=ne(e);if(!i||!n)return Qt(t,e);const s={...i};return t=>(s.red=ee(i.red,n.red,t),s.green=ee(i.green,n.green,t),s.blue=ee(i.blue,n.blue,t),s.alpha=te(i.alpha,n.alpha,t),Ut.transform(s))},oe=new Set(["none","hidden"]);function re(t,e){return i=>te(t,e,i)}function ae(t){return"number"==typeof t?re:"string"==typeof t?s(t)?Qt:Wt.test(t)?se:ue:Array.isArray(t)?le:"object"==typeof t?Wt.test(t)?se:he:Qt}function le(t,e){const i=[...t],n=i.length,s=t.map((t,i)=>ae(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function he(t,e){const i={...t,...e},n={};for(const s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=ae(t[s])(t[s],e[s]));return t=>{for(const e in n)i[e]=n[e](t);return i}}const ue=(t,e)=>{const i=Zt.createTransformer(e),n=Ht(t),s=Ht(e);return n.indexes.var.length===s.indexes.var.length&&n.indexes.color.length===s.indexes.color.length&&n.indexes.number.length>=s.indexes.number.length?oe.has(t)&&!s.values.length||oe.has(e)&&!n.values.length?function(t,e){return oe.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):tt(le(function(t,e){const i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][n[o]],a=t.values[r]??0;i[s]=a,n[o]++}return i}(n,s),s.values),i):Qt(t,e)};function ce(t,e,i){if("number"==typeof t&&"number"==typeof e&&"number"==typeof i)return te(t,e,i);return ae(t)(t,e)}const de=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>Et.update(e,t),stop:()=>Vt(e),now:()=>Mt.isProcessing?Mt.timestamp:Rt.now()}},pe=(t,e,i=10)=>{let n="";const s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`},me=2e4;function fe(t){let e=0;let i=t.next(e);for(;!i.done&&e<me;)e+=50,i=t.next(e);return e>=me?1/0:e}function ye(t,e,i){const n=Math.max(e-5,0);return ot(i-t(n),e-n)}const ge=100,ve=10,xe=1,Te=0,Pe=800,we=.3,Se=.3,Ae={granular:.01,default:2},be={granular:.005,default:.5},Ee=.01,Ve=10,Me=.05,De=1,Ce=.001;function ke({duration:t=Pe,bounce:e=we,velocity:n=Te,mass:s=xe}){let o,r,a=1-e;a=i(Me,De,a),t=i(Ee,Ve,st(t)),a<1?(o=e=>{const i=e*a,s=i*t,o=i-n,r=Le(e,a),l=Math.exp(-s);return Ce-o/r*l},r=e=>{const i=e*a*t,s=i*n+n,r=Math.pow(a,2)*Math.pow(e,2)*t,l=Math.exp(-i),h=Le(Math.pow(e,2),a);return(-o(e)+Ce>0?-1:1)*((s-r)*l)/h}):(o=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,r=e=>Math.exp(-e*t)*(t*t*(n-e)));const l=function(t,e,i){let n=i;for(let i=1;i<Re;i++)n-=t(n)/e(n);return n}(o,r,5/t);if(t=nt(t),isNaN(l))return{stiffness:ge,damping:ve,duration:t};{const e=Math.pow(l,2)*s;return{stiffness:e,damping:2*a*Math.sqrt(s*e),duration:t}}}const Re=12;function Le(t,e){return t*Math.sqrt(1-e*e)}const je=["duration","bounce"],Be=["stiffness","damping","mass"];function Fe(t,e){return e.some(e=>void 0!==t[e])}function Ie(t=Se,e=we){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:o}=n;const r=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:r},{stiffness:h,damping:u,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:Te,stiffness:ge,damping:ve,mass:xe,isResolvedFromDuration:!1,...t};if(!Fe(t,Be)&&Fe(t,je))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(1.2*n),o=s*s,r=2*i(.05,1,1-(t.bounce||0))*Math.sqrt(o);e={...e,mass:xe,stiffness:o,damping:r}}else{const i=ke(t);e={...e,...i,mass:xe},e.isResolvedFromDuration=!0}return e}({...n,velocity:-st(n.velocity||0)}),f=p||0,y=u/(2*Math.sqrt(h*c)),g=a-r,v=st(Math.sqrt(h/c)),x=Math.abs(g)<5;let T;if(s||(s=x?Ae.granular:Ae.default),o||(o=x?be.granular:be.default),y<1){const t=Le(v,y);T=e=>{const i=Math.exp(-y*v*e);return a-i*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===y)T=t=>a-Math.exp(-v*t)*(g+(f+v*g)*t);else{const t=v*Math.sqrt(y*y-1);T=e=>{const i=Math.exp(-y*v*e),n=Math.min(t*e,300);return a-i*((f+y*v*g)*Math.sinh(n)+t*g*Math.cosh(n))/t}}const P={calculatedDuration:m&&d||null,next:t=>{const e=T(t);if(m)l.done=t>=d;else{let i=0===t?f:0;y<1&&(i=0===t?nt(f):ye(T,t,e));const n=Math.abs(i)<=s,r=Math.abs(a-e)<=o;l.done=n&&r}return l.value=l.done?a:e,l},toString:()=>{const t=Math.min(fe(P),me),e=pe(e=>P.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return P}function Oe({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:h=.5,restSpeed:u}){const c=t[0],d={done:!1,value:c},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=i*e;const f=c+m,y=void 0===r?f:r(f);y!==f&&(m=y-c);const g=t=>-m*Math.exp(-t/n),v=t=>y+g(t),x=t=>{const e=g(t),i=v(t);d.done=Math.abs(e)<=h,d.value=d.done?y:i};let T,P;const w=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(T=t,P=Ie({keyframes:[d.value,p(d.value)],velocity:ye(v,t,d.value),damping:s,stiffness:o,restDelta:h,restSpeed:u}))};return w(0),{calculatedDuration:null,next:t=>{let e=!1;return P||void 0!==T||(e=!0,x(t),w(t)),void 0!==T&&t>=T?P.next(t-T):(!e&&x(t),d)}}}function Ue(t,e,{clamp:n=!0,ease:s,mixer:o}={}){const r=t.length;if(e.length,1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];const a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const l=function(t,e,i){const n=[],s=i||H.mix||ce,o=t.length-1;for(let i=0;i<o;i++){let o=s(t[i],t[i+1]);if(e){const t=Array.isArray(e)?e[i]||J:e;o=tt(t,o)}n.push(o)}return n}(e,s,o),h=l.length,u=i=>{if(a&&i<t[0])return e[0];let n=0;if(h>1)for(;n<t.length-2&&!(i<t[n+1]);n++);const s=et(t[n],t[n+1],i);return l[n](s)};return n?e=>u(i(t[0],t[r-1],e)):u}function Ne(t){const e=[0];return function(t,e){const i=t[t.length-1];for(let n=1;n<=e;n++){const s=et(0,e,n);t.push(te(i,1,s))}}(e,t.length-1),e}function Ke({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(n)?n.map(wt):wt(n),o={done:!1,value:e[0]},r=function(t,e){return t.map(t=>t*e)}(i&&i.length===e.length?i:Ne(e),t),a=Ue(r,e,{ease:Array.isArray(s)?s:(l=e,h=s,l.map(()=>h||xt).splice(0,l.length-1))});var l,h;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}Ie.applyToOptions=t=>{const e=function(t,e=100,i){const n=i({...t,keyframes:[0,e]}),s=Math.min(fe(n),me);return{type:"keyframes",ease:t=>n.next(s*t).value/e,duration:st(s)}}(t,100,Ie);return t.ease=e.ease,t.duration=nt(e.duration),t.type="keyframes",t};const We=t=>null!==t;function $e(t,{repeat:e,repeatType:i="loop"},n,s=1){const o=t.filter(We),r=s<0||e&&"loop"!==i&&e%2==1?0:o.length-1;return r&&void 0!==n?n:o[r]}const ze={decay:Oe,inertia:Oe,tween:Ke,keyframes:Ke,spring:Ie};function Ye(t){"string"==typeof t.type&&(t.type=ze[t.type])}class Xe{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const He=t=>t/100;class Ge extends Xe{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==Rt.now()&&this.tick(Rt.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Ye(t);const{type:e=Ke,repeat:i=0,repeatDelay:n=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||Ke;a!==Ke&&"number"!=typeof r[0]&&(this.mixKeyframes=tt(He,ce(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=fe(l));const{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:s,mixKeyframes:o,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:l}=this;if(null===this.startTime)return n.next(0);const{delay:h=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:p,type:m,onUpdate:f,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-h*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let x=this.currentTime,T=n;if(c){const t=Math.min(this.currentTime,s)/a;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,c+1);Boolean(e%2)&&("reverse"===d?(n=1-n,p&&(n-=p/a)):"mirror"===d&&(T=r)),x=i(0,1,n)*a}const P=v?{done:!1,value:u[0]}:T.next(x);o&&(P.value=o(P.value));let{done:w}=P;v||null===l||(w=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return S&&m!==Oe&&(P.value=$e(u,this.options,y,this.speed)),f&&f(P.value),S&&this.finish(),P}then(t,e){return this.finished.then(t,e)}get duration(){return st(this.calculatedDuration)}get iterationDuration(){const{delay:t=0}=this.options||{};return this.duration+st(t)}get time(){return st(this.currentTime)}set time(t){t=nt(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(Rt.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=st(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=de,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Rt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}const qe=t=>180*t/Math.PI,_e=t=>{const e=qe(Math.atan2(t[1],t[0]));return Je(e)},Ze={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:_e,rotateZ:_e,skewX:t=>qe(Math.atan(t[1])),skewY:t=>qe(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Je=t=>((t%=360)<0&&(t+=360),t),Qe=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ti=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ei={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Qe,scaleY:ti,scale:t=>(Qe(t)+ti(t))/2,rotateX:t=>Je(qe(Math.atan2(t[6],t[5]))),rotateY:t=>Je(qe(Math.atan2(-t[2],t[0]))),rotateZ:_e,rotate:_e,skewX:t=>qe(Math.atan(t[4])),skewY:t=>qe(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ii(t){return t.includes("scale")?1:0}function ni(t,e){if(!t||"none"===t)return ii(e);const i=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let n,s;if(i)n=ei,s=i;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=Ze,s=e}if(!s)return ii(e);const o=n[e],r=s[1].split(",").map(si);return"function"==typeof o?o(r):r[o]}function si(t){return parseFloat(t.trim())}const oi=e=>e===t||e===o,ri=new Set(["x","y","z"]),ai=r.filter(t=>!ri.has(t));const li={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ni(e,"x"),y:(t,{transform:e})=>ni(e,"y")};li.translateX=li.x,li.translateY=li.y;const hi=new Set;let ui=!1,ci=!1,di=!1;function pi(){if(ci){const t=Array.from(hi).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{const e=function(t){const e=[];return ai.forEach(i=>{const n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(i.startsWith("scale")?1:0))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ci=!1,ui=!1,hi.forEach(t=>t.complete(di)),hi.clear()}function mi(){hi.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ci=!0)})}class fi{constructor(t,e,i,n,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(hi.add(this),ui||(ui=!0,Et.read(mi),Et.resolveKeyframes(pi))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){const s=n?.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){const n=i.readValue(e,o);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=o),n&&void 0===s&&n.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),hi.delete(this)}cancel(){"scheduled"===this.state&&(hi.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const yi=Z(()=>void 0!==window.ScrollTimeline),gi={};function vi(t,e){const i=Z(t);return()=>gi[e]??i()}const xi=vi(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),Ti=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,Pi={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ti([0,.65,.55,1]),circOut:Ti([.55,0,1,.45]),backIn:Ti([.31,.01,.66,-.59]),backOut:Ti([.33,1.53,.69,.99])};function wi(t,e){return t?"function"==typeof t?xi()?pe(t,e):"ease-out":Tt(t)?Ti(t):Array.isArray(t)?t.map(t=>wi(t,e)||Pi.easeOut):Pi[t]:void 0}function Si(t,e,i,{delay:n=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},h=void 0){const u={[e]:i};l&&(u.offset=l);const c=wi(a,s);Array.isArray(c)&&(u.easing=c);const d={delay:n,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};h&&(d.pseudoElement=h);return t.animate(u,d)}function Ai(t){return"function"==typeof t&&"applyToOptions"in t}class bi extends Xe{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:i,keyframes:n,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,t.type;const l=function({type:t,...e}){return Ai(t)&&xi()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=Si(e,i,n,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=$e(n,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return st(Number(t))}get iterationDuration(){const{delay:t=0}=this.options||{};return this.duration+st(t)}get time(){return st(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=nt(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&yi()?(this.animation.timeline=t,J):e(this)}}const Ei={anticipate:pt,backInOut:dt,circInOut:yt};function Vi(t){"string"==typeof t.ease&&t.ease in Ei&&(t.ease=Ei[t.ease])}class Mi extends bi{constructor(t){Vi(t),Ye(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:i,onComplete:n,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new Ge({...o,autoplay:!1}),a=nt(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const Di=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Zt.test(t)&&"0"!==t||t.startsWith("url(")));function Ci(t){t.duration=0,t.type="keyframes"}const ki=new Set(["opacity","clipPath","filter","transform"]),Ri=Z(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class Li extends Xe{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=Rt.now();const c={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:h,...u},d=h?.KeyframeResolver||fi;this.keyframeResolver=new d(r,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),a,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:h}=i;this.resolvedAt=Rt.now(),function(t,e,i,n){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=Di(s,e),a=Di(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||Ai(i))&&n)}(t,s,o,r)||(!H.instantAnimations&&a||h?.($e(t,i,e)),t[0]=t[t.length-1],Ci(i),i.repeat=0);const u={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},c=!l&&function(t){const{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:o,type:r}=t,a=e?.owner?.current;if(!(a instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:h}=e.owner.getProps();return Ri()&&i&&ki.has(i)&&("transform"!==i||!h)&&!l&&!n&&"mirror"!==s&&0!==o&&"inertia"!==r}(u)?new Mi({...u,element:u.motionValue.owner.current}):new Ge(u);c.finished.then(()=>this.notifyFinished()).catch(J),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),di=!0,mi(),pi(),di=!1),this._animation}get duration(){return this.animation.duration}get iterationDuration(){return this.animation.iterationDuration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const ji=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Bi(t,e,i=1){const[n,o]=function(t){const e=ji.exec(t);if(!e)return[,];const[,i,n,s]=e;return[`--${i??n}`,s]}(t);if(!n)return;const r=window.getComputedStyle(e).getPropertyValue(n);if(r){const t=r.trim();return G(t)?parseFloat(t):t}return s(o)?Bi(o,e,i+1):o}function Fi(t,e){return t?.[e]??t?.default??t}const Ii=new Set(["width","height","top","left","right","bottom",...r]),Oi=t=>e=>e.test(t),Ui=[t,o,n,a,l,h,{test:t=>"auto"===t,parse:t=>t}],Ni=t=>Ui.find(Oi(t));function Ki(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||_(t))}const Wi=new Set(["brightness","contrast","saturate","opacity"]);function $i(t){const[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[n]=i.match(jt)||[];if(!n)return t;const s=i.replace(n,"");let o=Wi.has(e)?1:0;return n!==i&&(o*=100),e+"("+o+s+")"}const zi=/\b([a-z-]*)\(.*?\)/gu,Yi={...Zt,getAnimatableNone:t=>{const e=t.match(zi);return e?e.map($i).join(" "):t}},Xi={...u,color:Wt,backgroundColor:Wt,outlineColor:Wt,fill:Wt,stroke:Wt,borderColor:Wt,borderTopColor:Wt,borderRightColor:Wt,borderBottomColor:Wt,borderLeftColor:Wt,filter:Yi,WebkitFilter:Yi},Hi=t=>Xi[t];function Gi(t,e){let i=Hi(t);return i!==Yi&&(i=Zt),i.getAnimatableNone?i.getAnimatableNone(e):void 0}const qi=new Set(["auto","none","0"]);class _i extends fi{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&(n=n.trim(),s(n))){const s=Bi(n,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!Ii.has(i)||2!==t.length)return;const[n,o]=t,r=Ni(n),a=Ni(o);if(r!==a)if(oi(r)&&oi(a))for(let e=0;e<t.length;e++){const i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else li[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++)(null===t[e]||Ki(t[e]))&&i.push(e);i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){const e=t[s];"string"==typeof e&&!qi.has(e)&&Ht(e).values.length&&(n=t[s]),s++}if(n&&i)for(const s of e)t[s]=Gi(i,n)}(t,i,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=li[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;const n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);const s=i.length-1,o=i[s];i[s]=li[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}class Zi{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{const e=Rt.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=Rt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new it);const i=this.events[t].add(e);return"change"===t?()=>{i(),Et.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=Rt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return ot(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ji(t,e){return new Zi(t,e)}const{schedule:Qi,cancel:tn}=bt(queueMicrotask,!1),en={x:!1,y:!1};function nn(){return en.x||en.y}function sn(t,e){const i=function(t){if(t instanceof EventTarget)return[t];if("string"==typeof t){const e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function on(t){return!("touch"===t.pointerType||nn())}const rn=(t,e)=>!!e&&(t===e||rn(t,e.parentElement)),an=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,ln=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const hn=new WeakSet;function un(t){return e=>{"Enter"===e.key&&t(e)}}function cn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function dn(t){return an(t)&&!nn()}function pn(t,e,i={}){const[n,s,o]=sn(t,i),r=t=>{const n=t.currentTarget;if(!dn(t))return;hn.add(n);const o=e(n,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),hn.has(n)&&hn.delete(n),dn(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,n===window||n===document||i.useGlobalTarget||rn(n,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{var e;(i.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),q(e=t)&&"offsetHeight"in e&&(t.addEventListener("focus",t=>((t,e)=>{const i=t.currentTarget;if(!i)return;const n=un(()=>{if(hn.has(i))return;cn(i,"down");const t=un(()=>{cn(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>cn(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)})(t,s)),function(t){return ln.has(t.tagName)||-1!==t.tabIndex}(t)||t.hasAttribute("tabindex")||(t.tabIndex=0))}),o}function mn(t){return q(t)&&"ownerSVGElement"in t}const fn=[...Ui,Wt,Zt];class yn{constructor(t){this.isMounted=!1,this.node=t}update(){}}const gn=t=>null!==t;const vn={type:"spring",stiffness:500,damping:25,restSpeed:10},xn={type:"keyframes",duration:.8},Tn={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Pn=(t,{keyframes:e})=>e.length>2?xn:c.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:vn:Tn;const wn=(t,e,i,n={},s,o)=>r=>{const a=Fi(n,t)||{},l=a.delay||n.delay||0;let{elapsed:h=0}=n;h-=nt(l);const u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-h,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:h,...u}){return!!Object.keys(u).length})(a)||Object.assign(u,Pn(t,u)),u.duration&&(u.duration=nt(u.duration)),u.repeatDelay&&(u.repeatDelay=nt(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let c=!1;if((!1===u.type||0===u.duration&&!u.repeatDelay)&&(Ci(u),0===u.delay&&(c=!0)),(H.instantAnimations||H.skipAnimations)&&(c=!0,Ci(u),u.delay=0),u.allowFlatten=!a.type&&!a.ease,c&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:i="loop"},n){const s=t.filter(gn),o=e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}(u.keyframes,a);if(void 0!==t)return void Et.update(()=>{u.onUpdate(t),u.onComplete()})}return a.isSync?new Ge(u):new Li(u)};function Sn(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}function An(t){return{point:{x:t.pageX,y:t.pageY}}}function bn(t,e,i,n){return Sn(t,e,(t=>e=>an(e)&&t(e,An(e)))(i),n)}function En({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function Vn(t){return t.max-t.min}function Mn(t,e,i,n=.5){t.origin=n,t.originPoint=te(e.min,e.max,t.origin),t.scale=Vn(i)/Vn(e),t.translate=te(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function Dn(t,e,i,n){Mn(t.x,e.x,i.x,n?n.originX:void 0),Mn(t.y,e.y,i.y,n?n.originY:void 0)}function Cn(t,e,i){t.min=i.min+e.min,t.max=t.min+Vn(e)}function kn(t,e,i){t.min=e.min-i.min,t.max=t.min+Vn(e)}function Rn(t,e,i){kn(t.x,e.x,i.x),kn(t.y,e.y,i.y)}const Ln=()=>({x:{min:0,max:0},y:{min:0,max:0}});function jn(t){return[t("x"),t("y")]}function Bn(t){return void 0===t||1===t}function Fn({scale:t,scaleX:e,scaleY:i}){return!Bn(t)||!Bn(e)||!Bn(i)}function In(t){return Fn(t)||On(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function On(t){return Un(t.x)||Un(t.y)}function Un(t){return t&&"0%"!==t}function Nn(t,e,i){return i+e*(t-i)}function Kn(t,e,i,n,s){return void 0!==s&&(t=Nn(t,s,n)),Nn(t,i,n)+e}function Wn(t,e=0,i=1,n,s){t.min=Kn(t.min,e,i,n,s),t.max=Kn(t.max,e,i,n,s)}function $n(t,{x:e,y:i}){Wn(t.x,e.translate,e.scale,e.originPoint),Wn(t.y,i.translate,i.scale,i.originPoint)}const zn=.999999999999,Yn=1.0000000000001;function Xn(t,e){t.min=t.min+e,t.max=t.max+e}function Hn(t,e,i,n,s=.5){Wn(t,e,i,te(t.min,t.max,s),n)}function Gn(t,e){Hn(t.x,e.x,e.scaleX,e.scale,e.originX),Hn(t.y,e.y,e.scaleY,e.scale,e.originY)}function qn(t,e){return En(function(t,e){if(!e)return t;const i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}const _n=({current:t})=>t?t.ownerDocument.defaultView:null;function Zn(t,e){const i=t.getValue("willChange");if(n=i,Boolean(d(n)&&n.add))return i.add(e);if(!i&&H.WillChange){const i=new H.WillChange("auto");t.addValue("willChange",i),i.add(e)}var n}const Jn=(t,e)=>Math.abs(t-e);class Qn{constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:s=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=is(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){const i=Jn(t.x,e.x),n=Jn(t.y,e.y);return Math.sqrt(i**2+n**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;const{point:n}=t,{timestamp:s}=Mt;this.history.push({...n,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=ts(e,this.transformPagePoint),Et.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:i,onSessionEnd:n,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=is("pointercancel"===t.type?this.lastMoveEventInfo:ts(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,o),n&&n(t,o)},!an(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=o,this.contextWindow=n||window;const r=ts(An(t),this.transformPagePoint),{point:a}=r,{timestamp:l}=Mt;this.history=[{...a,timestamp:l}];const{onSessionStart:h}=e;h&&h(t,is(r,this.history)),this.removeListeners=tt(bn(this.contextWindow,"pointermove",this.handlePointerMove),bn(this.contextWindow,"pointerup",this.handlePointerUp),bn(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Vt(this.updatePoint)}}function ts(t,e){return e?{point:e(t.point)}:t}function es(t,e){return{x:t.x-e.x,y:t.y-e.y}}function is({point:t},e){return{point:t,delta:es(t,ss(e)),offset:es(t,ns(e)),velocity:os(e,.1)}}function ns(t){return t[0]}function ss(t){return t[t.length-1]}function os(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null;const s=ss(t);for(;i>=0&&(n=t[i],!(s.timestamp-n.timestamp>nt(e)));)i--;if(!n)return{x:0,y:0};const o=st(s.timestamp-n.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-n.x)/o,y:(s.y-n.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function rs(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function as(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}const ls=.35;function hs(t,e,i){return{min:us(t,e),max:us(t,i)}}function us(t,e){return"number"==typeof t?t:t[e]||0}const cs=new WeakMap;class ds{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){const{presenceContext:s}=this.visualElement;if(s&&!1===s.isPresent)return;const{dragSnapToOrigin:o}=this.getProps();this.panSession=new Qn(t,{onSessionStart:t=>{const{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(An(t).point)},onStart:(t,e)=>{const{drag:i,dragPropagation:s,onDragStart:o}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(r=i)||"y"===r?en[r]?null:(en[r]=!0,()=>{en[r]=!1}):en.x||en.y?null:(en.x=en.y=!0,()=>{en.x=en.y=!1}),!this.openDragLock))return;var r;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),jn(t=>{let e=this.getAxisMotionValue(t).get()||0;if(n.test(e)){const{projection:i}=this.visualElement;if(i&&i.layout){const n=i.layout.layoutBox[t];if(n){e=Vn(n)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),o&&Et.postRender(()=>o(t,e)),Zn(this.visualElement,"transform");const{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;const{dragPropagation:i,dragDirectionLock:n,onDirectionLock:s,onDrag:o}=this.getProps();if(!i&&!this.openDragLock)return;const{offset:r}=e;if(n&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let i=null;Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x");return i}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>jn(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:o,distanceThreshold:i,contextWindow:_n(this.visualElement)})}stop(t,e){const i=t||this.latestPointerEvent,n=e||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!n||!i)return;const{velocity:o}=n;this.startAnimation(o);const{onDragEnd:r}=this.getProps();r&&Et.postRender(()=>r(i,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){const{drag:n}=this.getProps();if(!i||!ps(t,n,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?te(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?te(i,t,n.max):Math.min(t,i)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&p(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!i)&&function(t,{top:e,left:i,bottom:n,right:s}){return{x:rs(t.x,i,s),y:rs(t.y,e,n)}}(i.layoutBox,t),this.elastic=function(t=ls){return!1===t?t=0:!0===t&&(t=ls),{x:hs(t,"left","right"),y:hs(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&jn(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!p(t))return!1;const i=t.current,{projection:n}=this.visualElement;if(!n||!n.layout)return!1;const s=function(t,e,i){const n=qn(t,i),{scroll:s}=e;return s&&(Xn(n.x,s.offset.x),Xn(n.y,s.offset.y)),n}(i,n.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:as(t.x,e.x),y:as(t.y,e.y)}}(n.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=En(t))}return o}startAnimation(t){const{drag:e,dragMomentum:i,dragElastic:n,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=jn(r=>{if(!ps(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const h=n?200:1e6,u=n?40:1e7,c={type:"inertia",velocity:i?t[r]:0,bounceStiffness:h,bounceDamping:u,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,c)});return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const i=this.getAxisMotionValue(t);return Zn(this.visualElement,t),i.start(wn(t,i,0,e,this.visualElement,!1))}stopAnimation(){jn(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){jn(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps(),n=i[e];return n||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){jn(e=>{const{drag:i}=this.getProps();if(!ps(e,i,this.currentDirection))return;const{projection:n}=this.visualElement,s=this.getAxisMotionValue(e);if(n&&n.layout){const{min:i,max:o}=n.layout.layoutBox[e];s.set(t[e]-te(i,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!p(e)||!n||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};jn(t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();s[t]=function(t,e){let n=.5;const s=Vn(t),o=Vn(e);return o>s?n=et(e.min,e.max-s,t.min):s>o&&(n=et(t.min,t.max-o,e.min)),i(0,1,n)}({min:n,max:n},this.constraints[t])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),jn(e=>{if(!ps(e,t,null))return;const i=this.getAxisMotionValue(e),{min:n,max:o}=this.constraints[e];i.set(te(n,o,s[e]))})}addListeners(){if(!this.visualElement.current)return;cs.set(this.visualElement,this);const t=bn(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{const{dragConstraints:t}=this.getProps();p(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),Et.read(e);const s=Sn(window,"resize",()=>this.scalePositionWithinConstraints()),o=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(jn(e=>{const i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),n(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:s=!1,dragElastic:o=ls,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function ps(t,e,i){return!(!0!==e&&e!==t||null!==i&&i!==t)}const ms=t=>(e,i)=>{t&&Et.postRender(()=>t(e,i))};function fs(t){return t.props[m]}const ys=(t,e)=>t.depth-e.depth;class gs{constructor(){this.children=[],this.isDirty=!1}add(t){Y(this.children,t),this.isDirty=!0}remove(t){X(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(ys),this.isDirty=!1,this.children.forEach(t)}}const vs=["TopLeft","TopRight","BottomLeft","BottomRight"],xs=vs.length,Ts=t=>"string"==typeof t?parseFloat(t):t,Ps=t=>"number"==typeof t||o.test(t);function ws(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Ss=bs(0,.5,ft),As=bs(.5,.95,J);function bs(t,e,i){return n=>n<t?0:n>e?1:i(et(t,e,n))}function Es(t,e){t.min=e.min,t.max=e.max}function Vs(t,e){Es(t.x,e.x),Es(t.y,e.y)}function Ms(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Ds(t,e,i,n,s){return t=Nn(t-=e,1/i,n),void 0!==s&&(t=Nn(t,1/s,n)),t}function Cs(t,e,[i,s,o],r,a){!function(t,e=0,i=1,s=.5,o,r=t,a=t){n.test(e)&&(e=parseFloat(e),e=te(a.min,a.max,e/100)-a.min);if("number"!=typeof e)return;let l=te(r.min,r.max,s);t===r&&(l-=e),t.min=Ds(t.min,e,i,l,o),t.max=Ds(t.max,e,i,l,o)}(t,e[i],e[s],e[o],e.scale,r,a)}const ks=["x","scaleX","originX"],Rs=["y","scaleY","originY"];function Ls(t,e,i,n){Cs(t.x,e,ks,i?i.x:void 0,n?n.x:void 0),Cs(t.y,e,Rs,i?i.y:void 0,n?n.y:void 0)}function js(t){return 0===t.translate&&1===t.scale}function Bs(t){return js(t.x)&&js(t.y)}function Fs(t,e){return t.min===e.min&&t.max===e.max}function Is(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Os(t,e){return Is(t.x,e.x)&&Is(t.y,e.y)}function Us(t){return Vn(t.x)/Vn(t.y)}function Ns(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Ks{constructor(){this.members=[]}add(t){Y(this.members,t),t.scheduleRender()}remove(t){if(X(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let i;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){i=e;break}}return!!i&&(this.promote(i),!0)}promote(t,e){const i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const Ws={hasAnimatedSinceResize:!0,hasEverUpdated:!1},$s=["","X","Y","Z"];let zs=0;function Ys(t,e,i,n){const{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),n&&(n[t]=0))}function Xs(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const i=fs(e);if(window.MotionHasOptimisedAnimation(i,"transform")){const{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(i,"transform",Et,!(e||n))}const{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&Xs(n)}function Hs({attachResizeListener:t,defaultParent:e,measureScroll:s,checkIsScrollRoot:o,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=zs++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(_s),this.nodes.forEach(no),this.nodes.forEach(so),this.nodes.forEach(Zs)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new gs)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new it),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;var i;this.isSVG=mn(e)&&!(mn(i=e)&&"svg"===i.tagName),this.instance=e;const{layoutId:n,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||n)&&(this.isLayoutDirty=!0),t){let i,n=0;const s=()=>this.root.updateBlockedByResize=!1;Et.read(()=>{n=window.innerWidth}),t(e,()=>{const t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){const i=Rt.now(),n=({timestamp:s})=>{const o=s-i;o>=e&&(Vt(n),t(o-e))};return Et.setup(n,!0),()=>Vt(n)}(s,250),Ws.hasAnimatedSinceResize&&(Ws.hasAnimatedSinceResize=!1,this.nodes.forEach(io)))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&o&&(n||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||uo,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!Os(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const e={...Fi(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||io(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Vt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(oo),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Xs(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;const n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Qs);if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(to);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(eo),this.nodes.forEach(Gs),this.nodes.forEach(qs)):this.nodes.forEach(to),this.clearAllSnapshots();const t=Rt.now();Mt.delta=i(0,1e3/60,t-Mt.timestamp),Mt.timestamp=t,Mt.isProcessing=!0,Dt.update.process(Mt),Dt.preRender.process(Mt),Dt.render.process(Mt),Mt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Qi.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Js),this.sharedNodes.forEach(ro)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Et.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Et.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||Vn(this.snapshot.measuredBox.x)||Vn(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=o(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!Bs(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||In(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let i=this.removeElementScroll(e);var n;return t&&(i=this.removeTransform(i)),mo((n=i).x),mo(n.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(yo))){const{scroll:t}=this.root;t&&(Xn(e.x,t.offset.x),Xn(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(Vs(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){const n=this.path[i],{scroll:s,options:o}=n;n!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&Vs(e,t),Xn(e.x,s.offset.x),Xn(e.y,s.offset.y))}return e}applyTransform(t,e=!1){const i={x:{min:0,max:0},y:{min:0,max:0}};Vs(i,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&Gn(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),In(n.latestValues)&&Gn(i,n.latestValues)}return In(this.latestValues)&&Gn(i,this.latestValues),i}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Vs(e,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];if(!i.instance)continue;if(!In(i.latestValues))continue;Fn(i.latestValues)&&i.updateSnapshot();const n=Ln();Vs(n,i.measurePageBox()),Ls(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return In(this.latestValues)&&Ls(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Mt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:n,layoutId:s}=this.options;if(this.layout&&(n||s)){if(this.resolvedRelativeTargetAt=Mt.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Rn(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Vs(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var o,r,a;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,r=this.relativeTarget,a=this.relativeParent.target,Cn(o.x,r.x,a.x),Cn(o.y,r.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Vs(this.target,this.layout.layoutBox),$n(this.target,this.targetDelta)):Vs(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Rn(this.relativeTargetOrigin,this.target,t.target),Vs(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!Fn(this.parent.latestValues)&&!On(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===Mt.timestamp&&(i=!1),i)return;const{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!n&&!s)return;Vs(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,r=this.treeScale.y;!function(t,e,i,n=!1){const s=i.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=i[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(n&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Gn(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,$n(t,r)),n&&In(o.latestValues)&&Gn(t,o.latestValues))}e.x<Yn&&e.x>zn&&(e.x=1),e.y<Yn&&e.y>zn&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(Ms(this.prevProjectionDelta.x,this.projectionDelta.x),Ms(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Dn(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===r&&Ns(this.projectionDelta.x,this.prevProjectionDelta.x)&&Ns(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const i=this.snapshot,s=i?i.latestValues:{},o={...this.latestValues},r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const a={x:{min:0,max:0},y:{min:0,max:0}},l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,c=Boolean(l&&!u&&!0===this.options.crossfade&&!this.path.some(ho));let d;this.animationProgress=0,this.mixTargetDelta=e=>{const i=e/1e3;var h,p,m,f,y,g;ao(r.x,t.x,i),ao(r.y,t.y,i),this.setTargetDelta(r),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Rn(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,f=this.relativeTargetOrigin,y=a,g=i,lo(m.x,f.x,y.x,g),lo(m.y,f.y,y.y,g),d&&(h=this.relativeTarget,p=d,Fs(h.x,p.x)&&Fs(h.y,p.y))&&(this.isProjectionDirty=!1),d||(d={x:{min:0,max:0},y:{min:0,max:0}}),Vs(d,this.relativeTarget)),l&&(this.animationValues=o,function(t,e,i,s,o,r){o?(t.opacity=te(0,i.opacity??1,Ss(s)),t.opacityExit=te(e.opacity??1,0,As(s))):r&&(t.opacity=te(e.opacity??1,i.opacity??1,s));for(let o=0;o<xs;o++){const r=`border${vs[o]}Radius`;let a=ws(e,r),l=ws(i,r);void 0===a&&void 0===l||(a||(a=0),l||(l=0),0===a||0===l||Ps(a)===Ps(l)?(t[r]=Math.max(te(Ts(a),Ts(l),s),0),(n.test(l)||n.test(a))&&(t[r]+="%")):t[r]=l)}(e.rotate||i.rotate)&&(t.rotate=te(e.rotate||0,i.rotate||0,s))}(o,s,this.latestValues,i,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(Vt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Et.update(()=>{Ws.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Ji(0)),this.currentAnimation=function(t,e,i){const n=d(t)?t:Ji(t);return n.start(wn("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:i,layout:n,latestValues:s}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&fo(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Vn(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;const n=Vn(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}Vs(e,i),Gn(e,s),Dn(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new Ks);this.sharedNodes.get(t).add(e);const i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){const n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;const n={};i.z&&Ys("z",t,n,this.animationValues);for(let e=0;e<$s.length;e++)Ys(`rotate${$s[e]}`,t,n,this.animationValues),Ys(`skew${$s[e]}`,t,n,this.animationValues);t.render();for(const e in n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return void(t.visibility="hidden");const i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=f(e?.pointerEvents)||"",void(t.transform=i?i(this.latestValues,""):"none");const n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target)return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=f(e?.pointerEvents)||""),void(this.hasProjected&&!In(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1));t.visibility="";const s=n.animationValues||n.latestValues;this.applyTransformsToTarget();let o=function(t,e,i){let n="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=i?.z||0;if((s||o||r)&&(n=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(n+=`scale(${1/e.x}, ${1/e.y}) `),i){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),s&&(n+=`rotateX(${s}deg) `),o&&(n+=`rotateY(${o}deg) `),r&&(n+=`skewX(${r}deg) `),a&&(n+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,s);i&&(o=i(s,o)),t.transform=o;const{x:r,y:a}=this.projectionDelta;t.transformOrigin=`${100*r.origin}% ${100*a.origin}% 0`,n.animationValues?t.opacity=n===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:t.opacity=n===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const e in y){if(void 0===s[e])continue;const{correct:i,applyTo:r,isCSSVariable:a}=y[e],l="none"===o?s[e]:i(s[e],n);if(r){const e=r.length;for(let i=0;i<e;i++)t[r[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?f(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(Qs),this.root.sharedNodes.clear()}}}function Gs(t){t.updateLayout()}function qs(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:i,measuredBox:n}=t.layout,{animationType:s}=t.options,o=e.source!==t.layout.source;"size"===s?jn(t=>{const n=o?e.measuredBox[t]:e.layoutBox[t],s=Vn(n);n.min=i[t].min,n.max=n.min+s}):fo(s,e.layoutBox,i)&&jn(n=>{const s=o?e.measuredBox[n]:e.layoutBox[n],r=Vn(i[n]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+r)});const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Dn(r,i,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Dn(a,t.applyTransform(n,!0),e.measuredBox):Dn(a,i,e.layoutBox);const l=!Bs(r);let h=!1;if(!t.resumeFrom){const n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){const{snapshot:s,layout:o}=n;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};Rn(r,e.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Rn(a,i,o.layoutBox),Os(r,a)||(h=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function _s(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Zs(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Js(t){t.clearSnapshot()}function Qs(t){t.clearMeasurements()}function to(t){t.isLayoutDirty=!1}function eo(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function io(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function no(t){t.resolveTargetDelta()}function so(t){t.calcProjection()}function oo(t){t.resetSkewAndRotation()}function ro(t){t.removeLeadSnapshot()}function ao(t,e,i){t.translate=te(e.translate,0,i),t.scale=te(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function lo(t,e,i,n){t.min=te(e.min,i.min,n),t.max=te(e.max,i.max,n)}function ho(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const uo={duration:.45,ease:[.4,0,.1,1]},co=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),po=co("applewebkit/")&&!co("chrome/")?Math.round:J;function mo(t){t.min=po(t.min),t.max=po(t.max)}function fo(t,e,i){return"position"===t||"preserve-aspect"===t&&(n=Us(e),s=Us(i),o=.2,!(Math.abs(n-s)<=o));var n,s,o}function yo(t){return t!==t.root&&t.scroll?.wasRoot}const go=Hs({attachResizeListener:(t,e)=>Sn(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),vo={current:void 0},xo=Hs({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!vo.current){const t=new go({});t.mount(window),t.setOptions({layoutScroll:!0}),vo.current=t}return vo.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)});function To(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Po={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!o.test(t))return t;t=parseFloat(t)}return`${To(t,e.target.x)}% ${To(t,e.target.y)}%`}},wo={correct:(t,{treeScale:e,projectionDelta:i})=>{const n=t,s=Zt.parse(t);if(s.length>5)return n;const o=Zt.createTransformer(t),r="number"!=typeof s[0]?1:0,a=i.x.scale*e.x,l=i.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const h=te(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=h),"number"==typeof s[3+r]&&(s[3+r]/=h),o(s)}},So={current:null},Ao={current:!1};const bo=new WeakMap;const Eo=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Vo{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=fi,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=Rt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,Et.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=v(e),this.isVariantNode=x(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in u){const e=u[t];void 0!==a[t]&&d(e)&&e.set(a[t])}}mount(t){this.current=t,bo.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),Ao.current||function(){if(Ao.current=!0,g)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>So.current=t.matches;t.addEventListener("change",e),e()}else So.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||So.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Vt(this.notifyUpdate),Vt(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const i=c.has(t);i&&this.onBindTransform&&this.onBindTransform();const n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&Et.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s&&s(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in T){const e=T[t];if(!e)continue;const{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<Eo.length;e++){const i=Eo[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(const n in e){const s=e[n],o=i[n];if(d(s))t.addValue(n,s);else if(d(o))t.addValue(n,Ji(s,{owner:t}));else if(o!==s)if(t.hasValue(n)){const e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(n);t.addValue(n,Ji(void 0!==e?e:s,{owner:t}))}}for(const n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=Ji(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var n;return null!=i&&("string"==typeof i&&(G(i)||_(i))?i=parseFloat(i):(n=i,!fn.find(Oi(n))&&Zt.test(e)&&(i=Gi(t,e))),this.setBaseTarget(t,d(i)?i.get():i)),d(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let i;if("string"==typeof e||"object"==typeof e){const n=P(this.props,e,this.presenceContext?.custom);n&&(i=n[t])}if(e&&void 0!==i)return i;const n=this.getBaseTargetFromProps(this.props,t);return void 0===n||d(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new it),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){Qi.render(this.render)}}class Mo extends Vo{constructor(){super(...arguments),this.KeyframeResolver=_i}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;d(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function Do(t,{style:e,vars:i},n,s){const o=t.style;let r;for(r in e)o[r]=e[r];for(r in s?.applyProjectionStyles(o,n),i)o.setProperty(r,i[r])}class Co extends Mo{constructor(){super(...arguments),this.type="html",this.renderInstance=Do}readValueFromInstance(t,e){if(c.has(e))return this.projection?.isProjecting?ii(e):((t,e)=>{const{transform:i="none"}=getComputedStyle(t);return ni(i,e)})(t,e);{const n=(i=t,window.getComputedStyle(i)),s=(w(e)?n.getPropertyValue(e):n[e])||0;return"string"==typeof s?s.trim():s}var i}measureInstanceViewportBox(t,{transformPagePoint:e}){return qn(t,e)}build(t,e,i){S(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return A(t,e,i)}}let ko=!1;class Ro extends ${componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:s}=t;M(jo),s&&(e.group&&e.group.add(s),i&&i.register&&n&&i.register(s),ko&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Ws.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:i,drag:n,isPresent:s}=this.props,{projection:o}=i;return o?(o.isPresent=s,ko=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||Et.postRender(()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Qi.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;ko=!0,n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Lo(t){const[e,i]=function(t=!0){const e=U(b);if(null===e)return[!0,null];const{isPresent:i,onExitComplete:n,register:s}=e,o=N();K(()=>{if(t)return s(o)},[t]);const r=W(()=>t&&n&&n(o),[o,n,t]);return!i&&n?[!1,r]:[!0]}(),n=U(E);return O(Ro,{...t,layoutGroup:n,switchLayoutGroup:U(V),isPresent:e,safeToRemove:i})}const jo={borderRadius:{...Po,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Po,borderTopRightRadius:Po,borderBottomLeftRadius:Po,borderBottomRightRadius:Po,boxShadow:wo},Bo={pan:{Feature:class extends yn{constructor(){super(...arguments),this.removePointerDownListener=J}onPointerDown(t){this.session=new Qn(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:_n(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:ms(t),onStart:ms(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&Et.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=bn(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends yn{constructor(t){super(t),this.removeGroupControls=J,this.removeListeners=J,this.controls=new ds(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||J}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:xo,MeasureLayout:Lo}},Fo={layout:{ProjectionNode:xo,MeasureLayout:Lo}};function Io(t,e,i){const n=t.getProps();return P(n,e,void 0!==i?i:n.custom,t)}const Oo=t=>Array.isArray(t);function Uo(t,e,i){t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,Ji(i))}function No(t){return Oo(t)?t[t.length-1]||0:t}function Ko({protectedKeys:t,needsAnimating:e},i){const n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}function Wo(t,e,{delay:i=0,transitionOverride:n,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;n&&(o=n);const l=[],h=s&&t.animationState&&t.animationState.getState()[s];for(const e in a){const n=t.getValue(e,t.latestValues[e]??null),s=a[e];if(void 0===s||h&&Ko(h,e))continue;const r={delay:i,...Fi(o||{},e)},u=n.get();if(void 0!==u&&!n.isAnimating&&!Array.isArray(s)&&s===u&&!r.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const i=fs(t);if(i){const t=window.MotionHandoffAnimation(i,e,Et);null!==t&&(r.startTime=t,c=!0)}}Zn(t,e),n.start(wn(e,n,s,t.shouldReduceMotion&&Ii.has(e)?{type:!1}:r,t,c));const d=n.animation;d&&l.push(d)}return r&&Promise.all(l).then(()=>{Et.update(()=>{r&&function(t,e){const i=Io(t,e);let{transitionEnd:n={},transition:s={},...o}=i||{};o={...o,...n};for(const e in o)Uo(t,e,No(o[e]))}(t,r)})}),l}function $o(t,e,i,n=0,s=1){const o=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),r=t.size,a=(r-1)*n;return"function"==typeof i?i(o,r):1===s?o*n:a-o*n}function zo(t,e,i={}){const n=Io(t,e,"exit"===i.type?t.presenceContext?.custom:void 0);let{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);const o=n?()=>Promise.all(Wo(t,n,i)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(n=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=s;return function(t,e,i=0,n=0,s=0,o=1,r){const a=[];for(const l of t.variantChildren)l.notify("AnimationStart",e),a.push(zo(l,e,{...r,delay:i+("function"==typeof n?0:n)+$o(t.variantChildren,l,n,s,o)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(a)}(t,e,n,o,r,a,i)}:()=>Promise.resolve(),{when:a}=s;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then(()=>e())}return Promise.all([o(),r(i.delay)])}function Yo(t,e){if(!Array.isArray(e))return!1;const i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}const Xo=C.length;function Ho(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&Ho(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let i=0;i<Xo;i++){const n=C[i],s=t.props[n];(D(s)||!1===s)&&(e[n]=s)}return e}const Go=[...R].reverse(),qo=R.length;function _o(t){return e=>Promise.all(e.map(({animation:e,options:i})=>function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map(e=>zo(t,e,i));n=Promise.all(s)}else if("string"==typeof e)n=zo(t,e,i);else{const s="function"==typeof e?Io(t,e,i.custom):e;n=Promise.all(Wo(t,s,i))}return n.then(()=>{t.notify("AnimationComplete",e)})}(t,e,i)))}function Zo(t){let e=_o(t),i=tr(),n=!0;const s=e=>(i,n)=>{const s=Io(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(s){const{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function o(o){const{props:r}=t,a=Ho(t.parent)||{},l=[],h=new Set;let u={},c=1/0;for(let e=0;e<qo;e++){const d=Go[e],p=i[d],m=void 0!==r[d]?r[d]:a[d],f=D(m),y=d===o?p.isActive:null;!1===y&&(c=e);let g=m===a[d]&&m!==r[d]&&f;if(g&&n&&t.manuallyAnimateOnMount&&(g=!1),p.protectedKeys={...u},!p.isActive&&null===y||!m&&!p.prevProp||k(m)||"boolean"==typeof m)continue;const v=Jo(p.prevProp,m);let x=v||d===o&&p.isActive&&!g&&f||e>c&&f,T=!1;const P=Array.isArray(m)?m:[m];let w=P.reduce(s(d),{});!1===y&&(w={});const{prevResolvedValues:S={}}=p,A={...S,...w},b=e=>{x=!0,h.has(e)&&(T=!0,h.delete(e)),p.needsAnimating[e]=!0;const i=t.getValue(e);i&&(i.liveStyle=!1)};for(const t in A){const e=w[t],i=S[t];if(u.hasOwnProperty(t))continue;let n=!1;n=Oo(e)&&Oo(i)?!Yo(e,i):e!==i,n?null!=e?b(t):h.add(t):void 0!==e&&h.has(t)?b(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=w,p.isActive&&(u={...u,...w}),n&&t.blockInitialAnimation&&(x=!1);const E=g&&v;x&&(!E||T)&&l.push(...P.map(e=>{const i={type:d};if("string"==typeof e&&n&&!E&&t.manuallyAnimateOnMount&&t.parent){const{parent:n}=t,s=Io(n,e);if(n.enteringChildren&&s){const{delayChildren:e}=s.transition||{};i.delay=$o(n.enteringChildren,t,e)}}return{animation:e,options:i}}))}if(h.size){const e={};if("boolean"!=typeof r.initial){const i=Io(t,Array.isArray(r.initial)?r.initial[0]:r.initial);i&&i.transition&&(e.transition=i.transition)}h.forEach(i=>{const n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=n??null}),l.push({animation:e})}let d=Boolean(l.length);return!n||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),n=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;const s=o(e);for(const t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=tr(),n=!0}}}function Jo(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Yo(e,t)}function Qo(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function tr(){return{animate:Qo(!0),whileInView:Qo(),whileHover:Qo(),whileTap:Qo(),whileDrag:Qo(),whileFocus:Qo(),exit:Qo()}}let er=0;const ir={animation:{Feature:class extends yn{constructor(t){super(t),t.animationState||(t.animationState=Zo(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();k(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends yn{constructor(){super(...arguments),this.id=er++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function nr(t,e,i){const{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);const s=n["onHover"+i];s&&Et.postRender(()=>s(e,An(e)))}function sr(t,e,i){const{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);const s=n["onTap"+("End"===i?"":i)];s&&Et.postRender(()=>s(e,An(e)))}const or=new WeakMap,rr=new WeakMap,ar=t=>{const e=or.get(t.target);e&&e(t)},lr=t=>{t.forEach(ar)};function hr(t,e,i){const n=function({root:t,...e}){const i=t||document;rr.has(i)||rr.set(i,{});const n=rr.get(i),s=JSON.stringify(e);return n[s]||(n[s]=new IntersectionObserver(lr,{root:t,...e})),n[s]}(e);return or.set(t,i),n.observe(t),()=>{or.delete(t),n.unobserve(t)}}const ur={some:0,all:1};const cr={inView:{Feature:class extends yn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:ur[n]};return hr(this.node.current,o,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),o=e?i:n;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends yn{mount(){const{current:t}=this.node;t&&(this.unmount=pn(t,(t,e)=>(sr(this.node,e,"Start"),(t,{success:e})=>sr(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends yn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tt(Sn(this.node.current,"focus",()=>this.onFocus()),Sn(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends yn{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){const[n,s,o]=sn(t,i),r=t=>{if(!on(t))return;const{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;const o=t=>{on(t)&&(n(t),i.removeEventListener("pointerleave",o))};i.addEventListener("pointerleave",o,s)};return n.forEach(t=>{t.addEventListener("pointerenter",r,s)}),o}(t,(t,e)=>(nr(this.node,e,"Start"),t=>nr(this.node,t,"End"))))}unmount(){}}}},dr=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class pr extends Mo{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Ln}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(c.has(e)){const t=Hi(e);return t&&t.default||0}return e=dr.has(e)?e:L(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return j(t,e,i)}build(t,e,i){B(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){!function(t,e,i,n){Do(t,e,void 0,n);for(const i in e.attrs)t.setAttribute(dr.has(i)?i:L(i),e.attrs[i])}(t,e,0,n)}mount(t){this.isSVGTag=F(t.tagName),super.mount(t)}}const mr={renderer:(t,e)=>I(t)?new pr(e):new Co(e,{allowProjection:t!==z}),...ir,...cr},fr={...mr,...Bo,...Fo};export{fr as domMax};
