function t(t){let e;return()=>(void 0===e&&(e=t()),e)}const e=t=>t,i=t=>1e3*t,n=t=>t/1e3,s=t=>null!==t;class a{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}function o(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const r=t=>t.startsWith("--");const l=t(()=>void 0!==window.ScrollTimeline),u={};function h(e,i){const n=t(e);return()=>u[i]??n()}const d=h(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),m=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,c={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:m([0,.65,.55,1]),circOut:m([.55,0,1,.45]),backIn:m([.31,.01,.66,-.59]),backOut:m([.33,1.53,.69,.99])};function p(t,e){return t?"function"==typeof t?d()?((t,e,i=10)=>{let n="";const s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`})(t,e):"ease-out":(t=>Array.isArray(t)&&"number"==typeof t[0])(t)?m(t):Array.isArray(t)?t.map(t=>p(t,e)||c.easeOut):c[t]:void 0}class f extends a{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:i,keyframes:n,pseudoElement:a,allowFlatten:o=!1,finalKeyframe:l,onComplete:u}=t;this.isPseudoElement=Boolean(a),this.allowFlatten=o,this.options=t,t.type;const h=function({type:t,...e}){return function(t){return"function"==typeof t&&"applyToOptions"in t}(t)&&d()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:s=300,repeat:a=0,repeatType:o="loop",ease:r="easeOut",times:l}={},u){const h={[e]:i};l&&(h.offset=l);const d=p(r,s);Array.isArray(d)&&(h.easing=d);const m={delay:n,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:a+1,direction:"reverse"===o?"alternate":"normal"};return u&&(m.pseudoElement=u),t.animate(h,m)}(e,i,n,h,a),!1===h.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!a){const t=function(t,{repeat:e,repeatType:i="loop"},n,a=1){const o=t.filter(s),r=a<0||e&&"loop"!==i&&e%2==1?0:o.length-1;return r&&void 0!==n?n:o[r]}(n,this.options,l,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){r(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}u?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return n(Number(t))}get iterationDuration(){const{delay:t=0}=this.options||{};return this.duration+n(t)}get time(){return n(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=i(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:i}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&l()?(this.animation.timeline=t,e):i(this)}}class y{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t){const e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){return g(this.animations,"duration")}get iterationDuration(){return g(this.animations,"iterationDuration")}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}function g(t,e){let i=0;for(let n=0;n<t.length;n++){const s=t[n][e];null!==s&&s>i&&(i=s)}return i}class T extends y{then(t,e){return this.finished.finally(t).then(()=>{})}}const b=new WeakMap,A=(t,e="")=>`${t}:${e}`;function w(t){const e=b.get(t)||new Map;return b.set(t,e),e}function v(t,e){return t?.[e]??t?.default??t}const E=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);function R(t,e){for(let i=0;i<t.length;i++)"number"==typeof t[i]&&E.has(e)&&(t[i]=t[i]+"px")}function S(t,e){const i=window.getComputedStyle(t);return r(e)?i.getPropertyValue(e):i[e]}function k(t,e,n,s){const a=function(t,e){if(t instanceof EventTarget)return[t];if("string"==typeof t){let i=document;e&&(i=e.current);const n=i.querySelectorAll(t);return n?Array.from(n):[]}return Array.from(t)}(t,s),r=a.length,l=[];for(let t=0;t<r;t++){const s=a[t],o={...n};"function"==typeof o.delay&&(o.delay=o.delay(t,r));for(const t in e){let n=e[t];Array.isArray(n)||(n=[n]);const a={...v(o,t)};a.duration&&(a.duration=i(a.duration)),a.delay&&(a.delay=i(a.delay));const r=w(s),u=A(t,a.pseudoElement||""),h=r.get(u);h&&h.stop(),l.push({map:r,key:u,unresolvedKeyframes:n,options:{...a,element:s,name:t,allowFlatten:!o.type&&!o.ease}})}}for(let t=0;t<l.length;t++){const{unresolvedKeyframes:e,options:i}=l[t],{element:n,name:s,pseudoElement:a}=i;a||null!==e[0]||(e[0]=S(n,s)),o(e),R(e,s),!a&&e.length<2&&e.unshift(S(n,s)),i.keyframes=e}const u=[];for(let t=0;t<l.length;t++){const{map:e,key:i,options:n}=l[t],s=new f(n);e.set(i,s),s.finished.finally(()=>e.delete(i)),u.push(s)}return u}const F=t=>function(e,i,n){return new T(k(e,i,n,t))},M=F();export{M as animateMini,F as createScopedWaapiAnimate};
