import{jsxs as t,jsx as n}from"react/jsx-runtime";import{createContext as r,useContext as e,useMemo as o,Fragment as a,createElement as i,useRef as s,useCallback as l,useLayoutEffect as c,useEffect as u,useInsertionEffect as f,forwardRef as d}from"react";const m=r({}),p=r({strict:!1}),y=r({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),g=r({});function h(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function v(t){return"string"==typeof t||Array.isArray(t)}const w=["initial","animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"];function b(t){return h(t.animate)||w.some(n=>v(t[n]))}function S(t){const{initial:n,animate:r}=function(t,n){if(b(t)){const{initial:n,animate:r}=t;return{initial:!1===n||v(n)?n:void 0,animate:v(r)?r:void 0}}return!1!==t.inherit?n:{}}(t,e(g));return o(()=>({initial:n,animate:r}),[x(n),x(r)])}function x(t){return Array.isArray(t)?t.join(" "):t}const M=(t=>n=>"string"==typeof n&&n.startsWith(t))("--"),P={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},T={...P,transform:t=>((t,n,r)=>r>n?n:r<t?t:r)(0,1,t)},k={...P,default:1},O=t=>({test:n=>"string"==typeof n&&n.endsWith(t)&&1===n.split(" ").length,parse:parseFloat,transform:n=>`${n}${t}`}),W=O("deg"),C=O("%"),L=O("px"),A=(()=>({...C,parse:t=>C.parse(t)/100,transform:t=>C.transform(100*t)}))(),E=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],j=(()=>new Set(E))(),B={...P,transform:Math.round},I={borderWidth:L,borderTopWidth:L,borderRightWidth:L,borderBottomWidth:L,borderLeftWidth:L,borderRadius:L,radius:L,borderTopLeftRadius:L,borderTopRightRadius:L,borderBottomRightRadius:L,borderBottomLeftRadius:L,width:L,maxWidth:L,height:L,maxHeight:L,top:L,right:L,bottom:L,left:L,padding:L,paddingTop:L,paddingRight:L,paddingBottom:L,paddingLeft:L,margin:L,marginTop:L,marginRight:L,marginBottom:L,marginLeft:L,backgroundPositionX:L,backgroundPositionY:L,...{rotate:W,rotateX:W,rotateY:W,rotateZ:W,scale:k,scaleX:k,scaleY:k,scaleZ:k,skew:W,skewX:W,skewY:W,distance:L,translateX:L,translateY:L,translateZ:L,x:L,y:L,z:L,perspective:L,transformPerspective:L,opacity:T,originX:A,originY:A,originZ:L},zIndex:B,fillOpacity:T,strokeOpacity:T,numOctaves:B},R=(t,n)=>n&&"number"==typeof t?n.transform(t):t,V=t=>Boolean(t&&t.getVelocity),X={};function Y(t,{layout:n,layoutId:r}){return j.has(t)||t.startsWith("origin")||(n||void 0!==r)&&(!!X[t]||"opacity"===t)}const $={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},D=E.length;function H(t,n,r){const{style:e,vars:o,transformOrigin:a}=t;let i=!1,s=!1;for(const t in n){const r=n[t];if(j.has(t))i=!0;else if(M(t))o[t]=r;else{const n=R(r,I[t]);t.startsWith("origin")?(s=!0,a[t]=n):e[t]=n}}if(n.transform||(i||r?e.transform=function(t,n,r){let e="",o=!0;for(let a=0;a<D;a++){const i=E[a],s=t[i];if(void 0===s)continue;let l=!0;if(l="number"==typeof s?s===(i.startsWith("scale")?1:0):0===parseFloat(s),!l||r){const t=R(s,I[i]);l||(o=!1,e+=`${$[i]||i}(${t}) `),r&&(n[i]=t)}}return e=e.trim(),r?e=r(n,o?"":e):o&&(e="none"),e}(n,t.transform,r):e.transform&&(e.transform="none")),s){const{originX:t="50%",originY:n="50%",originZ:r=0}=a;e.transformOrigin=`${t} ${n} ${r}`}}const F=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Z(t,n,r){for(const e in n)V(n[e])||Y(e,r)||(t[e]=n[e])}function z(t,n){const r={};return Z(r,t.style||{},t),Object.assign(r,function({transformTemplate:t},n){return o(()=>{const r={style:{},transform:{},transformOrigin:{},vars:{}};return H(r,n,t),Object.assign({},r.vars,r.style)},[n])}(t,n)),r}function N(t,n){const r={},e=z(t,n);return t.drag&&!1!==t.dragListener&&(r.draggable=!1,e.userSelect=e.WebkitUserSelect=e.WebkitTouchCallout="none",e.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(r.tabIndex=0),r.style=e,r}const U={offset:"stroke-dashoffset",array:"stroke-dasharray"},q={offset:"strokeDashoffset",array:"strokeDasharray"};function _(t,{attrX:n,attrY:r,attrScale:e,pathLength:o,pathSpacing:a=1,pathOffset:i=0,...s},l,c,u){if(H(t,s,c),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:f,style:d}=t;f.transform&&(d.transform=f.transform,delete f.transform),(d.transform||f.transformOrigin)&&(d.transformOrigin=f.transformOrigin??"50% 50%",delete f.transformOrigin),d.transform&&(d.transformBox=u?.transformBox??"fill-box",delete f.transformBox),void 0!==n&&(f.x=n),void 0!==r&&(f.y=r),void 0!==e&&(f.scale=e),void 0!==o&&function(t,n,r=1,e=0,o=!0){t.pathLength=1;const a=o?U:q;t[a.offset]=L.transform(-e);const i=L.transform(n),s=L.transform(r);t[a.array]=`${i} ${s}`}(f,o,a,i,!1)}const G=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}});function J(t,n,r,e){const a=o(()=>{const r={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};var o;return _(r,n,"string"==typeof(o=e)&&"svg"===o.toLowerCase(),t.transformTemplate,t.style),{...r.attrs,style:{...r.style}}},[n]);if(t.style){const n={};Z(n,t.style,t),a.style={...n,...a.style}}return a}const K=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Q(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||K.has(t)}let tt=t=>!Q(t);try{"function"==typeof(nt=require("@emotion/is-prop-valid").default)&&(tt=t=>t.startsWith("on")?!Q(t):nt(t))}catch{}var nt;const rt=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function et(t){return"string"==typeof t&&!t.includes("-")&&!!(rt.indexOf(t)>-1||/[A-Z]/u.test(t))}function ot(t,n,r,{latestValues:e},s,l=!1){const c=(et(t)?J:N)(n,e,s,t),u=function(t,n,r){const e={};for(const o in t)"values"===o&&"object"==typeof t.values||(tt(o)||!0===r&&Q(o)||!n&&!Q(o)||t.draggable&&o.startsWith("onDrag"))&&(e[o]=t[o]);return e}(n,"string"==typeof t,l),f=t!==a?{...u,...c,ref:r}:{},{children:d}=n,m=o(()=>V(d)?d.get():d,[d]);return i(t,{...f,children:m})}const at=r(null);function it(t,n,r,e){if("function"==typeof n){const[r,e]=[{},{}];n=n(t.custom,r,e)}if("string"==typeof n&&(n=t.variants&&t.variants[n]),"function"==typeof n){const[r,e]=[{},{}];n=n(t.custom,r,e)}return n}function st(t){return V(t)?t.get():t}function lt(t,n,r,e){const o={},a=e(t,{});for(const t in a)o[t]=st(a[t]);let{initial:i,animate:s}=t;const l=b(t),c=function(t){return Boolean(b(t)||t.variants)}(t);n&&c&&!l&&!1!==t.inherit&&(void 0===i&&(i=n.initial),void 0===s&&(s=n.animate));let u=!!r&&!1===r.initial;u=u||!1===i;const f=u?s:i;if(f&&"boolean"!=typeof f&&!h(f)){const n=Array.isArray(f)?f:[f];for(let r=0;r<n.length;r++){const e=it(t,n[r]);if(e){const{transitionEnd:t,transition:n,...r}=e;for(const t in r){let n=r[t];if(Array.isArray(n)){n=n[u?n.length-1:0]}null!==n&&(o[t]=n)}for(const n in t)o[n]=t[n]}}}return o}const ct=t=>(n,r)=>{const o=e(g),a=e(at),i=()=>function({scrapeMotionValuesFromProps:t,createRenderState:n},r,e,o){return{latestValues:lt(r,e,o,t),renderState:n()}}(t,n,o,a);return r?i():function(t){const n=s(null);return null===n.current&&(n.current=t()),n.current}(i)};function ut(t,n,r){const{style:e}=t,o={};for(const a in e)(V(e[a])||n.style&&V(n.style[a])||Y(a,t)||void 0!==r?.getValue(a)?.liveStyle)&&(o[a]=e[a]);return o}const ft=ct({scrapeMotionValuesFromProps:ut,createRenderState:F});const dt=ct({scrapeMotionValuesFromProps:function(t,n,r){const e=ut(t,n,r);for(const r in t)if(V(t[r])||V(n[r])){e[-1!==E.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=t[r]}return e},createRenderState:G}),mt="undefined"!=typeof window,pt={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},yt={};for(const t in pt)yt[t]={isEnabled:n=>pt[t].some(t=>!!n[t])};const gt=Symbol.for("motionComponentSymbol");function ht(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function vt(t,n,r){return l(e=>{e&&t.onMount&&t.onMount(e),n&&(e?n.mount(e):n.unmount()),r&&("function"==typeof r?r(e):ht(r)&&(r.current=e))},[n])}const wt="data-"+"framerAppearId".replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase();const bt=r({}),St=mt?c:u;function xt(t,n,r,o,a){const{visualElement:i}=e(g),l=e(p),c=e(at),d=e(y).reducedMotion,m=s(null);o=o||l.renderer,!m.current&&o&&(m.current=o(t,{visualState:n,parent:i,props:r,presenceContext:c,blockInitialAnimation:!!c&&!1===c.initial,reducedMotionConfig:d}));const h=m.current,v=e(bt);!h||h.projection||!a||"html"!==h.type&&"svg"!==h.type||function(t,n,r,e){const{layoutId:o,layout:a,drag:i,dragConstraints:s,layoutScroll:l,layoutRoot:c,layoutCrossfade:u}=n;t.projection=new r(t.latestValues,n["data-framer-portal-id"]?void 0:Mt(t.parent)),t.projection.setOptions({layoutId:o,layout:a,alwaysMeasureLayout:Boolean(i)||s&&ht(s),visualElement:t,animationType:"string"==typeof a?a:"both",initialPromotionConfig:e,crossfade:u,layoutScroll:l,layoutRoot:c})}(m.current,r,a,v);const w=s(!1);f(()=>{h&&w.current&&h.update(r,c)});const b=r[wt],S=s(Boolean(b)&&!window.MotionHandoffIsComplete?.(b)&&window.MotionHasOptimisedAnimation?.(b));return St(()=>{h&&(w.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),h.scheduleRenderMicrotask(),S.current&&h.animationState&&h.animationState.animateChanges())}),u(()=>{h&&(!S.current&&h.animationState&&h.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(b)}),S.current=!1),h.enteringChildren=void 0)}),h}function Mt(t){if(t)return!1!==t.options.allowProjection?t.projection:Mt(t.parent)}function Pt(r,{forwardMotionProps:o=!1}={},a,i){const s=et(r)?dt:ft;function l(a,l){let c;const u={...e(y),...a,layoutId:Tt(a)},{isStatic:f}=u,d=S(a),m=s(a,f);if(!f&&mt){e(p).strict;const t=function(t){const{drag:n,layout:r}=yt;if(!n&&!r)return{};const e={...n,...r};return{MeasureLayout:n?.isEnabled(t)||r?.isEnabled(t)?e.MeasureLayout:void 0,ProjectionNode:e.ProjectionNode}}(u);c=t.MeasureLayout,d.visualElement=xt(r,m,u,i,t.ProjectionNode)}return t(g.Provider,{value:d,children:[c&&d.visualElement?n(c,{visualElement:d.visualElement,...u}):null,ot(r,a,vt(m,d.visualElement,l),m,f,o)]})}l.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;const c=d(l);return c[gt]=r,c}function Tt({layoutId:t}){const n=e(m).id;return n&&void 0!==t?n+"-"+t:t}function kt(t,n){return Pt(t,n)}const Ot=kt("div");export{Ot as MotionDiv};
