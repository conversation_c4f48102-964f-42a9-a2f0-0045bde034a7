import{jsxs as t,jsx as n}from"react/jsx-runtime";import{createContext as o,useContext as e,useMemo as r,Fragment as a,createElement as i,useRef as s,useCallback as u,useLayoutEffect as c,useEffect as l,useInsertionEffect as d,forwardRef as f}from"react";import{i as m,a as p,b as y,c as g,d as v,e as h,f as S,g as M,P as b,r as w,h as j,j as C,k as E,s as P,l as A,m as T,n as L,S as W,o as x,L as I,p as k}from"./size-rollup-dom-max-assets.js";const O=o({strict:!1}),V=o({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),D=o({});function R(t){const{initial:n,animate:o}=function(t,n){if(m(t)){const{initial:n,animate:o}=t;return{initial:!1===n||p(n)?n:void 0,animate:p(o)?o:void 0}}return!1!==t.inherit?n:{}}(t,e(D));return r(()=>({initial:n,animate:o}),[H(n),H(o)])}function H(t){return Array.isArray(t)?t.join(" "):t}const N=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function F(t,n,o){for(const e in n)y(n[e])||g(e,o)||(t[e]=n[e])}function B(t,n){const o={};return F(o,t.style||{},t),Object.assign(o,function({transformTemplate:t},n){return r(()=>{const o={style:{},transform:{},transformOrigin:{},vars:{}};return v(o,n,t),Object.assign({},o.vars,o.style)},[n])}(t,n)),o}function q(t,n){const o={},e=B(t,n);return t.drag&&!1!==t.dragListener&&(o.draggable=!1,e.userSelect=e.WebkitUserSelect=e.WebkitTouchCallout="none",e.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(o.tabIndex=0),o.style=e,o}const U=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}});function $(t,n,o,e){const a=r(()=>{const o={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return h(o,n,S(e),t.transformTemplate,t.style),{...o.attrs,style:{...o.style}}},[n]);if(t.style){const n={};F(n,t.style,t),a.style={...n,...a.style}}return a}const _=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function z(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||_.has(t)}let X=t=>!z(t);try{"function"==typeof(Y=require("@emotion/is-prop-valid").default)&&(X=t=>t.startsWith("on")?!z(t):Y(t))}catch{}var Y;function G(t,n,o,{latestValues:e},s,u=!1){const c=(M(t)?$:q)(n,e,s,t),l=function(t,n,o){const e={};for(const r in t)"values"===r&&"object"==typeof t.values||(X(r)||!0===o&&z(r)||!n&&!z(r)||t.draggable&&r.startsWith("onDrag"))&&(e[r]=t[r]);return e}(n,"string"==typeof t,u),d=t!==a?{...l,...c,ref:o}:{},{children:f}=n,m=r(()=>y(f)?f.get():f,[f]);return i(t,{...d,children:m})}function J(t,n,o,e){const r={},a=e(t,{});for(const t in a)r[t]=w(a[t]);let{initial:i,animate:s}=t;const u=m(t),c=j(t);n&&c&&!u&&!1!==t.inherit&&(void 0===i&&(i=n.initial),void 0===s&&(s=n.animate));let l=!!o&&!1===o.initial;l=l||!1===i;const d=l?s:i;if(d&&"boolean"!=typeof d&&!C(d)){const n=Array.isArray(d)?d:[d];for(let o=0;o<n.length;o++){const e=E(t,n[o]);if(e){const{transitionEnd:t,transition:n,...o}=e;for(const t in o){let n=o[t];if(Array.isArray(n)){n=n[l?n.length-1:0]}null!==n&&(r[t]=n)}for(const n in t)r[n]=t[n]}}}return r}const K=t=>(n,o)=>{const r=e(D),a=e(b),i=()=>function({scrapeMotionValuesFromProps:t,createRenderState:n},o,e,r){return{latestValues:J(o,e,r,t),renderState:n()}}(t,n,r,a);return o?i():function(t){const n=s(null);return null===n.current&&(n.current=t()),n.current}(i)},Q=K({scrapeMotionValuesFromProps:P,createRenderState:N}),Z=K({scrapeMotionValuesFromProps:A,createRenderState:U}),tt=Symbol.for("motionComponentSymbol");function nt(t,n,o){return u(e=>{e&&t.onMount&&t.onMount(e),n&&(e?n.mount(e):n.unmount()),o&&("function"==typeof o?o(e):T(o)&&(o.current=e))},[n])}const ot=L?c:l;function et(t,n,o,r,a){const{visualElement:i}=e(D),u=e(O),c=e(b),f=e(V).reducedMotion,m=s(null);r=r||u.renderer,!m.current&&r&&(m.current=r(t,{visualState:n,parent:i,props:o,presenceContext:c,blockInitialAnimation:!!c&&!1===c.initial,reducedMotionConfig:f}));const p=m.current,y=e(W);!p||p.projection||!a||"html"!==p.type&&"svg"!==p.type||function(t,n,o,e){const{layoutId:r,layout:a,drag:i,dragConstraints:s,layoutScroll:u,layoutRoot:c,layoutCrossfade:l}=n;t.projection=new o(t.latestValues,n["data-framer-portal-id"]?void 0:rt(t.parent)),t.projection.setOptions({layoutId:r,layout:a,alwaysMeasureLayout:Boolean(i)||s&&T(s),visualElement:t,animationType:"string"==typeof a?a:"both",initialPromotionConfig:e,crossfade:l,layoutScroll:u,layoutRoot:c})}(m.current,o,a,y);const g=s(!1);d(()=>{p&&g.current&&p.update(o,c)});const v=o[x],h=s(Boolean(v)&&!window.MotionHandoffIsComplete?.(v)&&window.MotionHasOptimisedAnimation?.(v));return ot(()=>{p&&(g.current=!0,window.MotionIsMounted=!0,p.updateFeatures(),p.scheduleRenderMicrotask(),h.current&&p.animationState&&p.animationState.animateChanges())}),l(()=>{p&&(!h.current&&p.animationState&&p.animationState.animateChanges(),h.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(v)}),h.current=!1),p.enteringChildren=void 0)}),p}function rt(t){if(t)return!1!==t.options.allowProjection?t.projection:rt(t.parent)}function at(o,{forwardMotionProps:r=!1}={},a,i){const s=M(o)?Z:Q;function u(a,u){let c;const l={...e(V),...a,layoutId:it(a)},{isStatic:d}=l,f=R(a),m=s(a,d);if(!d&&L){e(O).strict;const t=function(t){const{drag:n,layout:o}=k;if(!n&&!o)return{};const e={...n,...o};return{MeasureLayout:n?.isEnabled(t)||o?.isEnabled(t)?e.MeasureLayout:void 0,ProjectionNode:e.ProjectionNode}}(l);c=t.MeasureLayout,f.visualElement=et(o,m,l,i,t.ProjectionNode)}return t(D.Provider,{value:f,children:[c&&f.visualElement?n(c,{visualElement:f.visualElement,...l}):null,G(o,a,nt(m,f.visualElement,u),m,d,r)]})}u.displayName=`motion.${"string"==typeof o?o:`create(${o.displayName??o.name??""})`}`;const c=f(u);return c[tt]=o,c}function it({layoutId:t}){const n=e(I).id;return n&&void 0!==t?n+"-"+t:t}function st(t,n){return at(t,n)}const ut=st("div");export{ut as MotionDiv};
