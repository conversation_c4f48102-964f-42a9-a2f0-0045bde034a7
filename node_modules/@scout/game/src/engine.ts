import type {
  Card,
  CardInHand,
  Orientation,
  PlayedCard,
  PlayedSet,
  PlayerId,
  RoundStatePublic,
  RoundStateForPlayer,
} from '@scout/protocol';
import { buildCanonicalDeck, filterDeckForPlayerCount, PlayerCount } from './deck';
import { beats, classifySet, sliceToPlayed } from './logic';

export interface PlayerState {
  id: PlayerId;
  name: string;
  hand: CardInHand[];
  facedown: string[]; // cardIds won
  scoutTokens: number;
  usedScoutShow: boolean;
}

export interface RoundResult {
  scores: Record<PlayerId, number>;
  winner?: PlayerId; // player who emptied their hand, if any
  reason: 'empty_hand' | 'all_passed';
}

export interface EngineConfig {
  playerIds: PlayerId[];
  playerNames: Record<PlayerId, string>;
  playerCount: PlayerCount;
  variant?: 'standard' | 'two-player';
  seed?: string;
}

export class Engine {
  readonly config: EngineConfig;
  private deckArray: Card[];
  private deckMap: Map<string, Card>;
  private players: Map<PlayerId, PlayerState> = new Map();
  private order: PlayerId[];
  private activeIndex = 0; // index in order
  private activeSet?: PlayedSet;
  private unbeatenSince?: PlayerId;

  constructor(config: EngineConfig) {
    this.config = config;
    const canonical = buildCanonicalDeck();
    const filtered = filterDeckForPlayerCount(canonical, config.playerCount);
    this.deckArray = shuffle(filtered, config.seed ?? 'seed');
    this.deckMap = new Map(this.deckArray.map((c) => [c.id, c]));
    this.order = [...config.playerIds];
    this.deal();
  }

  private deal() {
    // Deal counts per rules for 3-5p; 2p is variant dependent
    const pc = this.config.playerCount;
    let cardsPerPlayer: number;
    if (pc === 3) cardsPerPlayer = 12;
    else if (pc === 4) cardsPerPlayer = 11;
    else if (pc === 5) cardsPerPlayer = 9;
    else if (pc === 2) {
      // Provisional: 2p variant (to confirm). We'll use 11 each and place the remainder as a face-down bank.
      // This enables development and e2e; exact official counts will be swapped in later.
      cardsPerPlayer = 11;
    } else throw new Error('Unsupported player count');

    for (const pid of this.order) {
      const handCards: CardInHand[] = [];
      for (let i = 0; i < cardsPerPlayer; i++) {
        const card = this.deckArray.shift();
        if (!card) throw new Error('Deck exhausted');
        handCards.push({ cardId: card.id, orientation: 'a' });
      }
      this.players.set(pid, {
        id: pid,
        name: this.config.playerNames[pid],
        hand: handCards,
        facedown: [],
        scoutTokens: 0,
        usedScoutShow: false,
      });
    }
  }

  getPublicState(): RoundStatePublic {
    return {
      phase: 'playing',
      activePlayer: this.order[this.activeIndex],
      activeSet: this.activeSet,
      unbeatenSince: this.unbeatenSince,
      playerOrder: [...this.order],
      players: this.order.map((id) => {
        const p = this.players.get(id)!;
        return {
          id: p.id,
          name: p.name,
          handSize: p.hand.length,
          facedownCount: p.facedown.length,
          scoutTokens: p.scoutTokens,
          usedScoutShow: p.usedScoutShow,
        };
      }),
    };
  }

  getStateFor(pid: PlayerId): RoundStateForPlayer {
    const pub = this.getPublicState();
    const p = this.players.get(pid)!;
    return { ...pub, you: { id: p.id, hand: p.hand.map((c) => ({ ...c })) } };
  }

  chooseInitialHandOrientation(pid: PlayerId, orientation: Orientation) {
    // Flip entire hand to chosen orientation
    const p = this.players.get(pid);
    if (!p) throw new Error('No player');
    p.hand = p.hand.map((c) => ({ ...c, orientation }));
  }

  show(pid: PlayerId, start: number, endInclusive: number, orientations?: Orientation[]): { ok: boolean; error?: string } {
    if (this.order[this.activeIndex] !== pid) return { ok: false, error: 'Not your turn' };
    const p = this.players.get(pid)!;
    if (start < 0 || endInclusive >= p.hand.length || start > endInclusive) return { ok: false, error: 'Bad range' };
    // Slice the hand and classify
    const playedCards: PlayedCard[] = sliceToPlayed(p.hand, start, endInclusive, this.deckMap, orientations);
    const info = classifySet(playedCards);
    if (info.type === 'invalid') return { ok: false, error: 'Invalid set' };

    const candidate: PlayedSet = { ownerId: pid, type: info.type, cards: playedCards, length: info.length, minValue: info.minValue };
    if (!beats(candidate, this.activeSet)) return { ok: false, error: 'Does not beat active set' };

    // Take over active set and score previous as facedown
    if (this.activeSet) {
      const prevOwner = this.players.get(this.activeSet.ownerId)!;
      prevOwner.facedown.push(...this.activeSet.cards.map((c) => c.cardId));
    }

    // Remove from hand in-place
    p.hand.splice(start, playedCards.length);
    this.activeSet = candidate;
    this.unbeatenSince = pid;
    // Advance to next player
    this.activeIndex = (this.activeIndex + 1) % this.order.length;
    return { ok: true };
  }

  scout(pid: PlayerId, edge: 'left' | 'right', insertAt: number, orientation: Orientation, useScoutAndShow = false) {
    if (this.order[this.activeIndex] !== pid) return { ok: false, error: 'Not your turn' } as const;
    if (!this.activeSet) return { ok: false, error: 'No active set to scout from' } as const;
    const p = this.players.get(pid)!;
    if (insertAt < 0 || insertAt > p.hand.length) return { ok: false, error: 'Bad insert index' } as const;

    // Take edge card
    const idx = edge === 'left' ? 0 : this.activeSet.cards.length - 1;
    const taken = this.activeSet.cards.splice(idx, 1)[0];
    // Give token to owner of set
    const owner = this.players.get(this.activeSet.ownerId)!;
    owner.scoutTokens += 1;

    // Insert into hand with desired orientation
    p.hand.splice(insertAt, 0, { cardId: taken.cardId, orientation });

    // If set emptied, active set becomes undefined (documented rule nuance TBD)
    if (this.activeSet.cards.length === 0) {
      this.activeSet = undefined;
    }

    // Optionally allow immediate show once per round
    if (useScoutAndShow && !p.usedScoutShow) {
      p.usedScoutShow = true;
      // Turn does not advance here; caller should follow up with a show call in the same tick at UI level.
      return { ok: true, note: 'scouted; may now show' } as const;
    }

    // Advance to next player
    this.activeIndex = (this.activeIndex + 1) % this.order.length;
    return { ok: true } as const;
  }
}

// Simple deterministic shuffle (Fisher–Yates) seeded by string
function shuffle<T>(arr: T[], seed: string): T[] {
  const a = arr.slice();
  let s = xmur3(seed)();
  for (let i = a.length - 1; i > 0; i--) {
    s = xmur3(`${s}-${i}`)();
    const j = s % (i + 1);
    [a[i], a[j]] = [a[j], a[i]];
  }
  return a;
}

function xmur3(str: string) {
  // small xmur3 hash to generate a number from a string
  let h = 1779033703 ^ str.length;
  for (let i = 0; i < str.length; i++) {
    h = Math.imul(h ^ str.charCodeAt(i), 3432918353);
    h = (h << 13) | (h >>> 19);
  }
  return function () {
    h = Math.imul(h ^ (h >>> 16), 2246822507);
    h = Math.imul(h ^ (h >>> 13), 3266489909);
    return (h ^= h >>> 16) >>> 0;
  };
}
